import { Platform, Alert, AppState, AppStateStatus } from 'react-native';
import * as Application from 'expo-application';
import * as Device from 'expo-device';

/**
 * Global error handler for unhandled promise rejections and errors
 */
export class GlobalErrorHandler {
  private static isInitialized = false;
  private static errorCount = 0;
  private static readonly MAX_ERRORS_PER_SESSION = 10;

  static initialize() {
    if (this.isInitialized) return;

    // Handle unhandled promise rejections
    const originalHandler = global.Promise.prototype.catch;
    global.Promise.prototype.catch = function(onRejected) {
      return originalHandler.call(this, (error) => {
        GlobalErrorHandler.handleError(error, 'Unhandled Promise Rejection');
        if (onRejected) {
          return onRejected(error);
        }
        throw error;
      });
    };

    // Handle global errors
    if (global.ErrorUtils) {
      const originalGlobalHandler = global.ErrorUtils.getGlobalHandler();
      global.ErrorUtils.setGlobalHandler((error, isFatal) => {
        GlobalErrorHandler.handleError(error, isFatal ? 'Fatal Error' : 'Non-Fatal Error');
        if (originalGlobalHandler) {
          originalGlobalHandler(error, isFatal);
        }
      });
    }

    // Handle console errors
    const originalConsoleError = console.error;
    console.error = (...args) => {
      GlobalErrorHandler.handleError(new Error(args.join(' ')), 'Console Error');
      originalConsoleError.apply(console, args);
    };

    this.isInitialized = true;
    console.log('GlobalErrorHandler: Initialized');
  }

  private static handleError(error: Error, type: string) {
    // Filter out known non-critical errors to reduce log spam
    const errorMessage = error.message || '';
    const isKnownNonCriticalError =
      errorMessage.includes('removeEventListener is not a function') ||
      errorMessage.includes('SplashScreen') ||
      errorMessage.includes('Network request failed') ||
      errorMessage.includes('expo-navigation-bar');

    if (isKnownNonCriticalError) {
      // Only log these errors once per session to avoid spam
      if (this.errorCount === 0) {
        console.log(`GlobalErrorHandler: Known non-critical error detected (will not log repeatedly): ${errorMessage}`);
      }
      return;
    }

    this.errorCount++;

    // Only log critical errors
    if (this.errorCount <= 3) {
      console.warn(`GlobalErrorHandler: ${type} (${this.errorCount}/${this.MAX_ERRORS_PER_SESSION}):`, error.message);
    }

    // If too many errors, suggest app restart
    if (this.errorCount >= this.MAX_ERRORS_PER_SESSION) {
      this.showCriticalErrorAlert();
    }

    // Log error details for debugging (only for critical errors)
    if (this.errorCount <= 3) {
      this.logErrorDetails(error, type);
    }
  }

  private static showCriticalErrorAlert() {
    Alert.alert(
      'App Stability Warning',
      'The app has encountered multiple errors. Please restart the app for the best experience.',
      [
        { text: 'Continue', style: 'cancel' },
        { text: 'Restart', onPress: () => this.restartApp() }
      ]
    );
  }

  private static restartApp() {
    // In a production app, you might want to use a library like react-native-restart
    console.log('GlobalErrorHandler: App restart requested');
  }

  private static logErrorDetails(error: Error, type: string) {
    // Only log detailed error info for critical errors to reduce log spam
    const errorDetails = {
      type,
      message: error.message,
      // stack: error.stack, // Commented out to reduce log verbosity
      timestamp: new Date().toISOString(),
      platform: Platform.OS,
      version: Platform.Version,
      errorCount: this.errorCount,
    };

    // Only log if it's a critical error (first 3 errors)
    if (this.errorCount <= 3) {
      console.log('Error Details:', errorDetails);
    }
  }

  static reset() {
    this.errorCount = 0;
  }
}

/**
 * Memory pressure monitoring and management
 */
export class MemoryManager {
  private static memoryWarningCount = 0;
  private static readonly MAX_MEMORY_WARNINGS = 3;

  static initialize() {
    // Monitor app state changes
    AppState.addEventListener('change', this.handleAppStateChange);
    
    // Set up memory monitoring (iOS specific)
    if (Platform.OS === 'ios') {
      this.setupMemoryWarningListener();
    }

    console.log('MemoryManager: Initialized');
  }

  private static handleAppStateChange = (nextAppState: AppStateStatus) => {
    if (nextAppState === 'background') {
      // App is going to background, clean up resources
      this.performMemoryCleanup();
    } else if (nextAppState === 'active') {
      // App is becoming active, reset memory warning count
      this.memoryWarningCount = 0;
    }
  };

  private static setupMemoryWarningListener() {
    // This would typically use a native module to listen for memory warnings
    // For now, we'll simulate with a periodic check
    setInterval(() => {
      this.checkMemoryUsage();
    }, 30000); // Check every 30 seconds
  }

  private static checkMemoryUsage() {
    try {
      const memoryInfo = (global as any).performance?.memory;
      if (memoryInfo) {
        const usedMemory = memoryInfo.usedJSHeapSize;
        const totalMemory = memoryInfo.totalJSHeapSize;
        const memoryUsageRatio = usedMemory / totalMemory;

        if (memoryUsageRatio > 0.85) {
          this.handleMemoryPressure();
        }
      }
    } catch (error) {
      console.warn('MemoryManager: Memory check failed:', error);
    }
  }

  private static handleMemoryPressure() {
    this.memoryWarningCount++;
    console.warn(`MemoryManager: Memory pressure detected (${this.memoryWarningCount}/${this.MAX_MEMORY_WARNINGS})`);

    this.performMemoryCleanup();

    if (this.memoryWarningCount >= this.MAX_MEMORY_WARNINGS) {
      Alert.alert(
        'Memory Warning',
        'The app is using a lot of memory. Consider closing other apps or restarting this app.',
        [{ text: 'OK' }]
      );
    }
  }

  private static performMemoryCleanup() {
    try {
      // Force garbage collection if available
      if (global.gc) {
        global.gc();
      }

      // Clear any cached data
      console.log('MemoryManager: Performing memory cleanup');
    } catch (error) {
      console.warn('MemoryManager: Cleanup failed:', error);
    }
  }

  static cleanup() {
    try {
      // Use modern API if available, fallback to legacy
      if (AppState.removeEventListener) {
        AppState.removeEventListener('change', this.handleAppStateChange);
      }
    } catch (error) {
      console.log('MemoryManager: Error during cleanup (safe to ignore):', error.message);
    }
  }
}

/**
 * Network resilience manager
 */
export class NetworkResilienceManager {
  private static connectionFailures = 0;
  private static readonly MAX_CONNECTION_FAILURES = 5;

  static handleNetworkError(error: Error) {
    this.connectionFailures++;
    console.warn(`NetworkResilienceManager: Connection failure ${this.connectionFailures}/${this.MAX_CONNECTION_FAILURES}:`, error);

    if (this.connectionFailures >= this.MAX_CONNECTION_FAILURES) {
      Alert.alert(
        'Connection Issues',
        'Multiple network requests have failed. Please check your internet connection.',
        [{ text: 'OK' }]
      );
    }
  }

  static resetFailureCount() {
    this.connectionFailures = 0;
  }
}

/**
 * Device capability checker
 */
export class DeviceCapabilityChecker {
  private static capabilities: any = null;

  static async initialize() {
    try {
      this.capabilities = {
        deviceType: Device.deviceType,
        platform: Platform.OS,
        version: Platform.Version,
        isDevice: Device.isDevice,
        brand: Device.brand,
        modelName: Device.modelName,
        osName: Device.osName,
        osVersion: Device.osVersion,
      };

      console.log('DeviceCapabilityChecker: Device capabilities:', this.capabilities);
    } catch (error) {
      console.warn('DeviceCapabilityChecker: Failed to get device info:', error);
    }
  }

  static isLowEndDevice(): boolean {
    if (!this.capabilities) return false;

    // Simple heuristic for low-end devices
    if (Platform.OS === 'android') {
      // Android version below 8.0 or specific low-end indicators
      return parseInt(Platform.Version as string) < 26;
    } else if (Platform.OS === 'ios') {
      // iOS version below 13.0
      return parseFloat(Platform.Version as string) < 13.0;
    }

    return false;
  }

  static getOptimizedSettings() {
    const isLowEnd = this.isLowEndDevice();
    
    return {
      animationDuration: isLowEnd ? 150 : 300,
      imageQuality: isLowEnd ? 0.6 : 0.8,
      maxConcurrentRequests: isLowEnd ? 2 : 4,
      enableHeavyAnimations: !isLowEnd,
      cacheSize: isLowEnd ? 50 : 100,
    };
  }
}

/**
 * Main crash prevention system
 */
export class CrashPreventionSystem {
  private static isInitialized = false;

  static async initialize() {
    if (this.isInitialized) return;

    try {
      console.log('CrashPreventionSystem: Initializing...');

      // Initialize all subsystems
      GlobalErrorHandler.initialize();
      MemoryManager.initialize();
      await DeviceCapabilityChecker.initialize();

      this.isInitialized = true;
      console.log('CrashPreventionSystem: Successfully initialized');
    } catch (error) {
      console.error('CrashPreventionSystem: Initialization failed:', error);
    }
  }

  static cleanup() {
    MemoryManager.cleanup();
    GlobalErrorHandler.reset();
    NetworkResilienceManager.resetFailureCount();
    console.log('CrashPreventionSystem: Cleaned up');
  }

  static getSystemStatus() {
    return {
      initialized: this.isInitialized,
      deviceCapabilities: DeviceCapabilityChecker.getOptimizedSettings(),
      memoryWarnings: MemoryManager['memoryWarningCount'],
      networkFailures: NetworkResilienceManager['connectionFailures'],
    };
  }
}

export default CrashPreventionSystem;
