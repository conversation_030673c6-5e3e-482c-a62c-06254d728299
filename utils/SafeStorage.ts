import AsyncStorage from '@react-native-async-storage/async-storage';
import * as SecureStore from 'expo-secure-store';

/**
 * Safe AsyncStorage wrapper with error handling and retry logic
 */
export class SafeAsyncStorage {
  private static readonly MAX_RETRIES = 3;
  private static readonly RETRY_DELAY = 1000; // 1 second

  private static async delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  private static async executeWithRetry<T>(
    operation: () => Promise<T>,
    retries: number = this.MAX_RETRIES
  ): Promise<T | null> {
    for (let attempt = 1; attempt <= retries; attempt++) {
      try {
        return await operation();
      } catch (error) {
        console.warn(`SafeAsyncStorage: Attempt ${attempt} failed:`, error);
        
        if (attempt === retries) {
          console.error('SafeAsyncStorage: All retry attempts failed:', error);
          return null;
        }
        
        await this.delay(this.RETRY_DELAY * attempt);
      }
    }
    return null;
  }

  static async getItem(key: string): Promise<string | null> {
    if (!key || typeof key !== 'string') {
      console.warn('SafeAsyncStorage: Invalid key provided to getItem');
      return null;
    }

    return this.executeWithRetry(async () => {
      const value = await AsyncStorage.getItem(key);
      return value;
    });
  }

  static async setItem(key: string, value: string): Promise<boolean> {
    if (!key || typeof key !== 'string') {
      console.warn('SafeAsyncStorage: Invalid key provided to setItem');
      return false;
    }

    if (value === null || value === undefined) {
      console.warn('SafeAsyncStorage: Invalid value provided to setItem');
      return false;
    }

    const result = await this.executeWithRetry(async () => {
      await AsyncStorage.setItem(key, value);
      return true;
    });

    return result === true;
  }

  static async removeItem(key: string): Promise<boolean> {
    if (!key || typeof key !== 'string') {
      console.warn('SafeAsyncStorage: Invalid key provided to removeItem');
      return false;
    }

    const result = await this.executeWithRetry(async () => {
      await AsyncStorage.removeItem(key);
      return true;
    });

    return result === true;
  }

  static async multiGet(keys: string[]): Promise<[string, string | null][] | null> {
    if (!Array.isArray(keys) || keys.length === 0) {
      console.warn('SafeAsyncStorage: Invalid keys array provided to multiGet');
      return null;
    }

    return this.executeWithRetry(async () => {
      const result = await AsyncStorage.multiGet(keys);
      return result;
    });
  }

  static async multiSet(keyValuePairs: [string, string][]): Promise<boolean> {
    if (!Array.isArray(keyValuePairs) || keyValuePairs.length === 0) {
      console.warn('SafeAsyncStorage: Invalid keyValuePairs array provided to multiSet');
      return false;
    }

    const result = await this.executeWithRetry(async () => {
      await AsyncStorage.multiSet(keyValuePairs);
      return true;
    });

    return result === true;
  }

  static async clear(): Promise<boolean> {
    const result = await this.executeWithRetry(async () => {
      await AsyncStorage.clear();
      return true;
    });

    return result === true;
  }

  static async getAllKeys(): Promise<string[] | null> {
    return this.executeWithRetry(async () => {
      const keys = await AsyncStorage.getAllKeys();
      return keys;
    });
  }
}

/**
 * Safe SecureStore wrapper with error handling
 */
export class SafeSecureStore {
  private static readonly MAX_RETRIES = 2;
  private static readonly RETRY_DELAY = 500;

  private static async delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  private static async executeWithRetry<T>(
    operation: () => Promise<T>,
    retries: number = this.MAX_RETRIES
  ): Promise<T | null> {
    for (let attempt = 1; attempt <= retries; attempt++) {
      try {
        return await operation();
      } catch (error) {
        console.warn(`SafeSecureStore: Attempt ${attempt} failed:`, error);
        
        if (attempt === retries) {
          console.error('SafeSecureStore: All retry attempts failed:', error);
          return null;
        }
        
        await this.delay(this.RETRY_DELAY * attempt);
      }
    }
    return null;
  }

  static async getItemAsync(key: string): Promise<string | null> {
    if (!key || typeof key !== 'string') {
      console.warn('SafeSecureStore: Invalid key provided to getItemAsync');
      return null;
    }

    return this.executeWithRetry(async () => {
      const value = await SecureStore.getItemAsync(key);
      return value;
    });
  }

  static async setItemAsync(key: string, value: string): Promise<boolean> {
    if (!key || typeof key !== 'string') {
      console.warn('SafeSecureStore: Invalid key provided to setItemAsync');
      return false;
    }

    if (!value || typeof value !== 'string') {
      console.warn('SafeSecureStore: Invalid value provided to setItemAsync');
      return false;
    }

    const result = await this.executeWithRetry(async () => {
      await SecureStore.setItemAsync(key, value);
      return true;
    });

    return result === true;
  }

  static async deleteItemAsync(key: string): Promise<boolean> {
    if (!key || typeof key !== 'string') {
      console.warn('SafeSecureStore: Invalid key provided to deleteItemAsync');
      return false;
    }

    const result = await this.executeWithRetry(async () => {
      await SecureStore.deleteItemAsync(key);
      return true;
    });

    return result === true;
  }
}

/**
 * Unified storage interface that automatically chooses between AsyncStorage and SecureStore
 */
export class UnifiedStorage {
  // Keys that should be stored securely
  private static readonly SECURE_KEYS = [
    'auth_token',
    'refresh_token',
    'user_credentials',
    'api_key',
    'phone_number',
    'otp_verification',
  ];

  private static shouldUseSecureStore(key: string): boolean {
    return this.SECURE_KEYS.some(secureKey => key.includes(secureKey));
  }

  static async getItem(key: string): Promise<string | null> {
    if (this.shouldUseSecureStore(key)) {
      return SafeSecureStore.getItemAsync(key);
    } else {
      return SafeAsyncStorage.getItem(key);
    }
  }

  static async setItem(key: string, value: string): Promise<boolean> {
    if (this.shouldUseSecureStore(key)) {
      return SafeSecureStore.setItemAsync(key, value);
    } else {
      return SafeAsyncStorage.setItem(key, value);
    }
  }

  static async removeItem(key: string): Promise<boolean> {
    if (this.shouldUseSecureStore(key)) {
      return SafeSecureStore.deleteItemAsync(key);
    } else {
      return SafeAsyncStorage.removeItem(key);
    }
  }

  /**
   * Clear all storage (both AsyncStorage and SecureStore)
   * Use with caution!
   */
  static async clearAll(): Promise<boolean> {
    try {
      const asyncResult = await SafeAsyncStorage.clear();
      
      // Clear known secure keys
      const secureResults = await Promise.all(
        this.SECURE_KEYS.map(key => SafeSecureStore.deleteItemAsync(key))
      );
      
      return asyncResult && secureResults.every(result => result);
    } catch (error) {
      console.error('UnifiedStorage: Error clearing all storage:', error);
      return false;
    }
  }
}

export default UnifiedStorage;
