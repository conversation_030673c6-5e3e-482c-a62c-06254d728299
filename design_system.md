# JunkChk Design System

This document outlines the core design principles, visual elements, and components for the JunkChk application, heavily inspired by the aesthetic of the World App screenshots provided. The goal is to create a clean, minimalist, trustworthy, and highly usable interface.

## 1. Principles

The design of JunkChk should adhere to the following core principles, derived from the World App style:

- **Minimalism & Clarity:** Reduce visual clutter. Focus on essential information and actions. Every element should serve a clear purpose.
- **High Contrast:** Use strong contrast between text and background for optimal readability.
- **Typography-Driven Hierarchy:** Use font weights, sizes, and spacing effectively to guide the user's eye and communicate the importance of information.
- **Purposeful Color:** Use color sparingly and intentionally, primarily for status indicators (success, error, warning) and brand/content identity (like the grade colors A-E). The core UI should remain largely monochromatic.
- **Consistent Spacing:** Apply a consistent spacing scale to create visual harmony and clear separation between elements.
- **Clear Feedback:** Use icons, colors, and simple animations (like spinners) to provide immediate feedback on user actions and system status.

## 2. Color Palette

The palette is predominantly grayscale with specific accent colors used for status and key information.

- **Primary Colors:**

  - `Color.BackgroundPrimary`: `#FFFFFF` (Pure White) - Main background for screens and cards.
  - `Color.DarkText`: `#1A1A1A` (Very Dark Gray/Near Black) - Primary text color, button backgrounds, primary icons.
  - `Color.LightText`: `#7F7F7F` (Medium Gray) - Secondary text, descriptions, labels, subtle hints.
  - `Color.SurfaceSecondary`: `#F5F5F5` (Very Light Gray) - Subtle backgrounds, dividers, input field fills.

- **Accent / Status Colors:**

  - `Color.Success`: `#00CC00` (Bright Green) - Used for success states, positive results (Grade A, good ingredients), checkmarks.
  - `Color.Error`: `#FF0000` (Bright Red) - Used for error states, warnings, negative results (Grade E, bad ingredients), delete actions.
  - `Color.Warning`: `#FFA500` (Orange) - Used for warning states, intermediate negative results (Grade D).
  - `Color.Info`: `#FFD700` (Gold/Yellow) - Used for intermediate results (Grade C).
  - `Color.AccentBlue`: `#007AFF` (Standard iOS Blue, seen in links like "User Terms And Conditions") - Used for interactive links.

- **Grade Specific Colors (JunkChk Addition):**
  - `Color.GradeA`: `Color.Success` (`#00CC00`)
  - `Color.GradeB`: `#2ECC71` (Slightly less vibrant green)
  - `Color.GradeC`: `Color.Info` (`#FFD700`)
  - `Color.GradeD`: `Color.Warning` (`#FFA500`)
  - `Color.GradeE`: `Color.Error` (`#FF0000`)

**Example Usage:**

- Screen backgrounds: `Color.BackgroundPrimary`
- Main text: `Color.DarkText`
- Description text: `Color.LightText`
- Primary button background: `Color.DarkText`
- Success message icon/text: `Color.Success`
- Error pop-up icon/text: `Color.Error`
- Grade A display: Text/icon color `Color.GradeA`
- Grade E display: Text/icon color `Color.GradeE`

## 3. Typography

A modern, sans-serif typeface similar to SF Pro is ideal. If SF Pro is not available cross-platform, consider using a well-regarded alternative like Inter or Roboto. The key is consistent weights and sizes.

- **Font Family:** `System Font` (SF Pro on iOS, Roboto on Android typically) or `Inter` (cross-platform)

- **Text Styles:**
  - `Typography.Heading1`:
    - Size: `28-34px` (Adjust based on screen size)
    - Weight: `Bold` (e.g., `700`)
    - Use: Main screen titles ("Account created", "World App", "Learn about the Orb", "Let's connect your phone number", "Delete your data"). Centered or Left-aligned based on context.
  - `Typography.Heading2`:
    - Size: `20-24px`
    - Weight: `Bold` or `SemiBold` (e.g., `600`)
    - Use: Section titles, component titles (e.g., "Crypto" card title, "Settings" title).
  - `Typography.BodyText`:
    - Size: `16-18px`
    - Weight: `Regular` (e.g., `400`)
    - Use: Main body content, descriptions ("The wallet designed...", "Your account has been successfully created.", "The Orb is an optic verification device...").
  - `Typography.Description`:
    - Size: `14-16px`
    - Weight: `Regular` (e.g., `400`)
    - Use: Smaller descriptive text, hints ("This may take several seconds", "Use it to restore your backups...", "You can delete this data any time.").
  - `Typography.Caption`:
    - Size: `12-13px`
    - Weight: `Regular`
    - Use: Smallest text, labels, version numbers ("Version 2.5.13").
  - `Typography.ButtonText`:
    - Size: `16-18px`
    - Weight: `SemiBold` or `Bold` (e.g., `600` or `700`)
    - Use: Text on buttons.

**Example Usage:**

- "Account created" title: `Typography.Heading1`
- "Your account has been successfully created": `Typography.BodyText`
- "New account" button text: `Typography.ButtonText`
- "Create new Worldcoin account": `Typography.Description`
- "Didn't get the code? Please wait 29s": `Typography.Description`

## 4. Spacing

Use a consistent spacing scale to ensure rhythm and visual hierarchy. A base unit of 8px is common and effective.

- **Base Unit:** `Spacing.Unit = 8` (px)
- **Scale:**
  - `Spacing.ExtraSmall`: `Spacing.Unit * 0.5 = 4`
  - `Spacing.Small`: `Spacing.Unit * 1 = 8`
  - `Spacing.Medium`: `Spacing.Unit * 2 = 16`
  - `Spacing.Large`: `Spacing.Unit * 3 = 24`
  - `Spacing.ExtraLarge`: `Spacing.Unit * 4 = 32`
  - `Spacing.XXLarge`: `Spacing.Unit * 6 = 48`
  - `Spacing.XXXLarge`: `Spacing.Unit * 8 = 64`

**Example Usage:**

- Padding inside buttons: Vertical `Spacing.Medium`, Horizontal `Spacing.Large`.
- Spacing between title and description: `Spacing.Medium` or `Spacing.Large`.
- Spacing between major sections/cards: `Spacing.ExtraLarge`.
- Padding around screen content: Horizontal `Spacing.Large`, Vertical `Spacing.Large` or `Spacing.ExtraLarge`.
- Spacing between items in a list: `Spacing.Medium`.

## 5. Icons

Icons should be minimalist, clean, and easily understandable. Stick to a consistent style (e.g., outline or filled, but choose one predominantly).

- **Style:** Minimalist, line icons or solid shapes, often within circles or simple containers.
- **Color:** Default to `Color.DarkText`. Use `Color.Success` for positive actions/states, `Color.Error` for negative/warning actions/states. Use white (`Color.BackgroundPrimary`) on dark backgrounds (like primary buttons).
- **Common Icons Needed for JunkChk (in addition to navigation/status icons):**
  - Scan/Camera
  - History/Clock
  - Comparison/Balance or Scale
  - Settings/Gear
  - User/Profile
  - Allergies/Warning or Shield
  - Subscription/Star or Crown
  - Food/Apple or Utensils
  - Cosmetics/Lipstick or Jar
  - Grade Indicators (A, B, C, D, E - perhaps colored circles with the letter inside or distinct shapes)
  - Good Ingredient/Checkmark (`Color.Success`)
  - Bad Ingredient/Cross (`Color.Error`)
  - Info/Question Mark or 'i'
  - Delete/Trash Can (`Color.Error`)

**Example Usage:**

- Back arrow: Dark icon
- Checkmark on "Account created": Green icon within a light circle.
- Icons in Settings list: Dark icons.
- Trash can in Delete modal: Red icon within a light circle.

## 6. Components

Define reusable UI components with consistent styling.

- **Button:**

  - **Primary:** `backgroundColor: Color.DarkText`, `color: Color.BackgroundPrimary`, `borderRadius: 8-12px`, `paddingVertical: Spacing.Medium`, `paddingHorizontal: Spacing.Large`. `Typography: Typography.ButtonText`.
  - **Secondary:** `backgroundColor: Color.SurfaceSecondary` or `Color.BackgroundPrimary`, `color: Color.DarkText`, `borderRadius: 8-12px`, `paddingVertical: Spacing.Medium`, `paddingHorizontal: Spacing.Large`. `Typography: Typography.ButtonText`. Border optional.
  - **Tertiary (Text/Link):** Transparent background, `color: Color.AccentBlue` or `Color.LightText`, `Typography: Typography.BodyText`.
  - **Icon Button:** Minimal padding, just the icon. Used for Back/Close buttons.
  - **States:** Add styles for `pressed` (slight opacity change or scale down) and `disabled` (reduced opacity, maybe grayed out).

- **Input Field:**

  - Minimalist style, e.g., underline or a subtle background like `Color.SurfaceSecondary` with minimal border radius.
  - `paddingVertical: Spacing.Medium`, `paddingHorizontal: Spacing.Medium`.
  - `color: Color.DarkText`, `Typography: Typography.BodyText`.
  - Clear focus state (e.g., underline color changes).
  - Error state (e.g., underline color changes to `Color.Error`).

- **Card:**

  - `backgroundColor: Color.BackgroundPrimary` or `Color.SurfaceSecondary`.
  - `borderRadius: 12-16px`.
  - `padding: Spacing.Large`.
  - Optional subtle `shadow` (iOS) / `elevation` (Android).
  - Used for grouping content like Crypto view, Settings sections, Scan Results block.

- **List Row:**

  - Used within Cards or directly on a screen.
  - `paddingVertical: Spacing.Medium`, `paddingHorizontal: Spacing.Large`.
  - Optional separator line (1px, `color: Color.SurfaceSecondary`).
  - Content (Icon, Text, optional Description, Right Arrow icon).
  - Apply appropriate Typography styles.

- **Checkbox:**

  - Custom styled square (e.g., 24x24px).
  - Unchecked: `borderWidth: 2px`, `borderColor: Color.LightText`.
  - Checked: `backgroundColor: Color.DarkText`, `borderColor: Color.DarkText`, with a white checkmark icon (`Color.BackgroundPrimary`).

- **Status Indicator:**

  - A colored circle (e.g., `Success` green, `Error` red) containing an icon (checkmark, exclamation mark, trash can).
  - Circle size: approx 60-80px diameter.
  - Icon size: approx 30-40px.

- **Modal / Pop-up:**

  - Full screen overlay with a translucent background.
  - Content view centered, with `backgroundColor: Color.BackgroundPrimary`.
  - Rounded top corners (`borderRadius: 16-24px`).
  - `padding: Spacing.Large` all around content.
  - Often contains a Status Indicator, Title (`Typography.Heading2`), Description (`Typography.BodyText`), and Action Buttons at the bottom.

- **Progress Indicator:**
  - Circular spinner (color `Color.DarkText` or an accent color).
  - Text labels below (`Typography.Description`).

## 7. Layouts & Screen Structure

Maintain consistent screen padding and content alignment.

- **Screen Padding:** Apply horizontal padding (`Spacing.Large`) to the main content container on most screens to prevent content from touching the screen edges. Vertical padding/margins should utilize the spacing scale to separate major sections.
- **Alignment:** Titles can be centered or left-aligned based on the screen's purpose (centered for status/onboarding, left-aligned for content/settings). Body text and lists are typically left-aligned.
- **Navigation:** Use a clear back arrow icon (`<` or `←`) in the top left for navigating back. Use a close icon (`X`) for dismissing modals or flows.
- **Content Structure:** Use Cards and List Rows to organize information clearly, especially in Settings, History, and Scan Results details.

## 8. Applying to JunkChk Specifics

- **Onboarding:** Follow the World App flow: Welcome (World App screen 2 inspiration), Terms & Privacy (checkbox like screen 2), Account Creation (similar to screen 3 & 1). Phone number connection (screen 4 & 5) could be optional or part of profile setup later. Orb step (screen 7, 8, 9) is unique to World App, replace with a "Learn about JunkChk" or "How it Works" screen.
- **Scan Screen:** Needs a camera view. Design controls (flash, switch camera, capture) using the Button and Icon Button components with the defined colors.
- **Scan Results Screen:** This is a core screen. Use a Card component for the main result block. Display the Grade using `Typography.Heading1` with the corresponding `Color.GradeX`. Display the Product Name (`Typography.Heading2`). Use `Typography.BodyText` for the summary. Use List Rows or bullet points within the Card for the good and bad ingredients, using `Color.Success` and `Color.Error` for icons next to them.
- **History Screen:** Use a List Row component style for each item, perhaps showing the product name, date, and grade (colored icon/text).
- **Comparison Screen:** Use Card components side-by-side or stacked, displaying condensed scan results for comparison.
- **Settings Screen:** Follow the structure in screenshot 11. Use List Rows grouped potentially by Cards. Include Profile, Allergies (manage list - maybe a List Row that opens a new screen), Subscription, Support, About/Legal. Account deletion can use the modal style shown in screenshot 12.
- **Allergy Input:** A screen or modal allowing users to type or select allergens. Use the Input Field component style. Display selected allergies using tags or a list.

## 9. React Native Implementation Notes

- **Styling:** Use `StyleSheet.create` for defining styles to keep them organized and performant.
- **Color Management:** Define colors as constants (e.g., `const Colors = { ... }`) and import them where needed.
- **Typography:** Create reusable Text components that apply the defined font sizes, weights, and colors based on a `type` prop (`<Text type="heading1">`).
- **Spacing:** Create reusable components or hooks that apply padding/margins based on the spacing scale units (e.g., `<View style={{ marginVertical: Spacing.Medium }} />`).
- **Components:** Build reusable functional components for `Button`, `Card`, `ListRow`, `InputField`, `Modal`, `Checkbox`, etc., accepting props for content and behavior.
- **Icons:** Use a library like `react-native-vector-icons` and choose a font set that closely matches the desired minimalist style (e.g., Feather, MaterialCommunityIcons, Ionicons).

---

This design system provides a solid foundation to build your JunkChk app with the same clean, modern, and trustworthy feel as the World App, while incorporating the specific features and information your app needs to display. Remember to refer back to the screenshots frequently during development to ensure visual fidelity. Good luck!
