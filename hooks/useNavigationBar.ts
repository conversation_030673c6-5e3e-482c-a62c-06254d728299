import { useEffect } from 'react';
import { Platform, AppState } from 'react-native';
import * as NavigationBar from 'expo-navigation-bar';
import * as SystemUI from 'expo-system-ui';

interface NavigationBarOptions {
  visibility?: 'visible' | 'hidden';
  behavior?: 'overlay-swipe' | 'inset-swipe' | 'inset-touch';
  backgroundColor?: string;
  buttonStyle?: 'light' | 'dark';
  immersiveMode?: boolean;
}

/**
 * Hook to manage Android navigation bar visibility and styling
 * Enhanced for game-like immersive experience
 * @param options Configuration options for the navigation bar
 */
export const useNavigationBar = (options: NavigationBarOptions = {}) => {
  const {
    visibility = 'hidden',
    behavior = 'overlay-swipe',
    backgroundColor = '#000000',
    buttonStyle = 'light',
    immersiveMode = true
  } = options;

  useEffect(() => {
    const configureNavigationBar = async () => {
      // Only apply to Android
      if (Platform.OS === 'android') {
        try {
          // For immersive mode, use more aggressive hiding
          if (immersiveMode && visibility === 'hidden') {
            // Set background to transparent/black for seamless experience
            await NavigationBar.setBackgroundColorAsync('#00000000');

            // Use overlay-swipe for game-like experience
            await NavigationBar.setBehaviorAsync('overlay-swipe');

            // Hide the navigation bar
            await NavigationBar.setVisibilityAsync('hidden');

            // Set button style to light for better visibility when swiped up
            await NavigationBar.setButtonStyleAsync('light');

            // Also try to set system UI root view background
            try {
              await SystemUI.setBackgroundColorAsync('#000000');
            } catch (systemUIError) {
              console.log('SystemUI not available, continuing with NavigationBar only');
            }

            console.log('Android navigation bar configured for immersive mode');
          } else {
            // Standard configuration
            await NavigationBar.setVisibilityAsync(visibility);

            if (visibility === 'hidden') {
              await NavigationBar.setBehaviorAsync(behavior);
            }

            if (backgroundColor) {
              await NavigationBar.setBackgroundColorAsync(backgroundColor);
            }

            await NavigationBar.setButtonStyleAsync(buttonStyle);

            console.log(`Android navigation bar configured: visibility=${visibility}, behavior=${behavior}`);
          }
        } catch (error) {
          console.error('Error configuring navigation bar:', error);

          // Fallback: try basic hiding if advanced features fail
          try {
            await NavigationBar.setVisibilityAsync('hidden');
            console.log('Fallback navigation bar hiding applied');
          } catch (fallbackError) {
            console.error('Fallback navigation bar configuration failed:', fallbackError);
          }
        }
      }
    };

    configureNavigationBar();

    // Re-apply configuration when app comes to foreground
    const handleAppStateChange = (nextAppState: string) => {
      if (Platform.OS === 'android' && nextAppState === 'active') {
        setTimeout(configureNavigationBar, 100);
      }
    };

    // Listen for app state changes to re-apply settings
    let subscription: any = null;
    try {
      subscription = AppState.addEventListener('change', handleAppStateChange);
    } catch (error) {
      console.log('AppState.addEventListener not available, skipping app state monitoring');
    }

    return () => {
      try {
        if (subscription?.remove) {
          subscription.remove();
        } else if (subscription) {
          // Fallback for older React Native versions
          AppState.removeEventListener?.('change', handleAppStateChange);
        }
      } catch (error) {
        console.log('Error removing AppState listener:', error);
      }
    };
  }, [visibility, behavior, backgroundColor, buttonStyle, immersiveMode]);

  return null;
};

/**
 * Hook to hide Android navigation bar with immersive game-like settings
 * This is a convenience hook for the most common use case
 */
export const useHideNavigationBar = () => {
  return useNavigationBar({
    visibility: 'hidden',
    behavior: 'overlay-swipe',
    buttonStyle: 'light',
    immersiveMode: true,
    backgroundColor: '#00000000'
  });
};

/**
 * Hook to show Android navigation bar
 * Useful for screens where you want the navigation bar visible
 */
export const useShowNavigationBar = () => {
  return useNavigationBar({
    visibility: 'visible',
    buttonStyle: 'dark',
    immersiveMode: false
  });
};

/**
 * Hook for full immersive mode (game-like experience)
 * Hides navigation bar completely with transparent background
 */
export const useImmersiveMode = () => {
  return useNavigationBar({
    visibility: 'hidden',
    behavior: 'overlay-swipe',
    buttonStyle: 'light',
    immersiveMode: true,
    backgroundColor: '#00000000'
  });
};

export default useNavigationBar;
