import React, { memo, useMemo } from 'react';
import {
  View,
  SafeAreaView,
  StyleSheet,
  ViewStyle,
  StatusBar,
  ScrollView,
  KeyboardAvoidingView,
  Platform,
} from 'react-native';
import { Colors, Spacing } from '../theme';

interface ScreenProps {
  children: React.ReactNode;
  style?: ViewStyle;
  scrollable?: boolean;
  keyboardAvoiding?: boolean;
  backgroundColor?: string;
  safeArea?: boolean;
  contentContainerStyle?: ViewStyle;
}

const Screen: React.FC<ScreenProps> = memo(({
  children,
  style,
  scrollable = false,
  keyboardAvoiding = false,
  backgroundColor = Colors.BackgroundPrimary,
  safeArea = true,
  contentContainerStyle,
}) => {
  const Container = useMemo(() => safeArea ? SafeAreaView : View, [safeArea]);
  const Content = useMemo(() => scrollable ? ScrollView : View, [scrollable]);

  const containerStyle = useMemo(() => [
    styles.container,
    { backgroundColor },
    style
  ], [backgroundColor, style]);

  const contentContainerStyleMemo = useMemo(() => [
    scrollable && styles.scrollContent,
    contentContainerStyle,
  ], [scrollable, contentContainerStyle]);

  const renderContent = useMemo(() => (
    <Content
      style={styles.content}
      contentContainerStyle={contentContainerStyleMemo}
    >
      {children}
    </Content>
  ), [Content, contentContainerStyleMemo, children]);

  return (
    <Container style={containerStyle}>
      <StatusBar
        barStyle="dark-content"
        backgroundColor={Colors.BackgroundPrimary}
      />
      {keyboardAvoiding ? (
        <KeyboardAvoidingView
          style={styles.keyboardAvoidingView}
          behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
        >
          {renderContent}
        </KeyboardAvoidingView>
      ) : (
        renderContent
      )}
    </Container>
  );
});

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  content: {
    flex: 1,
  },
  scrollContent: {
    flexGrow: 1,
    paddingHorizontal: Spacing.Large,
    paddingVertical: Spacing.Large,
  },
  keyboardAvoidingView: {
    flex: 1,
  },
});

export default Screen;
