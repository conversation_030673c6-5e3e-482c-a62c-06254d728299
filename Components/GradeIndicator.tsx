import React from 'react';
import { View, StyleSheet, ViewStyle, Text, Platform } from 'react-native';
import { Colors, Typography } from '../theme';

// Robust font fallback configuration
const getFontFamily = () => {
  return Platform.select({
    ios: 'Poppins-Bold', // Use Bold instead of Black for better compatibility
    android: 'Poppins-Bold',
    default: Platform.select({
      ios: 'System',
      android: 'sans-serif-black',
      default: 'System'
    })
  });
};

interface GradeIndicatorProps {
  grade: 'A' | 'B' | 'C' | 'D' | 'E';
  size?: 'small' | 'medium' | 'large';
  style?: ViewStyle;
}

const GradeIndicator: React.FC<GradeIndicatorProps> = ({
  grade,
  size = 'medium',
  style,
}) => {
  // Ensure we have a valid grade with more robust checking
  const validGrade = (() => {
    if (!grade) {
      console.warn('GradeIndicator: No grade provided, defaulting to A');
      return 'A';
    }

    const upperGrade = String(grade).toUpperCase();
    if (!['A', 'B', 'C', 'D', 'E'].includes(upperGrade)) {
      console.warn(`GradeIndicator: Invalid grade "${grade}", defaulting to A`);
      return 'A';
    }

    return upperGrade as 'A' | 'B' | 'C' | 'D' | 'E';
  })();

  // Debug logging - commented out for production
  // console.log(`GradeIndicator rendering: originalGrade=${grade}, validGrade=${validGrade}, size=${size}, fontFamily=${getFontFamily()}`);
  const getGradeColor = () => {
    switch (validGrade) {
      case 'A':
        return Colors.GradeA;
      case 'B':
        return Colors.GradeB;
      case 'C':
        return Colors.GradeC;
      case 'D':
        return Colors.GradeD;
      case 'E':
        return Colors.GradeE;
      default:
        return Colors.GradeA;
    }
  };

  const getFontSize = () => {
    switch (size) {
      case 'small':
        return 16;
      case 'medium':
        return 24;
      case 'large':
        return 70;
      default:
        return 24;
    }
  };

  const getContainerSize = () => {
    switch (size) {
      case 'small':
        return 32;
      case 'medium':
        return 48;
      case 'large':
        return 120;
      default:
        return 48;
    }
  };

  return (
    <View
      style={[
        styles.container,
        {
          backgroundColor: getGradeColor(),
          width: getContainerSize(),
          height: getContainerSize(),
        },
        style,
      ]}
    >
      <View style={styles.textContainer}>
        <Text
          style={[
            styles.gradeText,
            {
              fontSize: getFontSize(),
              fontFamily: getFontFamily()
            }
          ]}
        >
          {validGrade}
        </Text>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    alignItems: 'center',
    justifyContent: 'center',
    borderRadius: 9999, // Round border radius
    overflow: 'hidden',
  },
  textContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    width: '100%',
    height: '100%',
  },
  gradeText: {
    fontWeight: Platform.select({
      ios: '900',
      android: '900',
      default: 'bold'
    }),
    textAlign: 'center',
    color: Colors.BackgroundPrimary,
    letterSpacing: 0,
    includeFontPadding: false, // Android specific - removes extra padding
    textAlignVertical: 'center', // Android specific - centers text vertically
    // Ensure text is properly positioned
    lineHeight: undefined, // Let the system calculate line height
    // Text shadow properties for better visibility
    ...(Platform.OS === 'ios' ? {
      textShadowColor: 'rgba(0, 0, 0, 0.3)',
      textShadowOffset: { width: 0, height: 1 },
      textShadowRadius: 2,
    } : {
      // Android text shadow alternative using elevation
      elevation: 2,
    }),
  },
});

export default GradeIndicator;
