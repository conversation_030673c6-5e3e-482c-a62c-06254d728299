import React from 'react';
import { View, StyleSheet, ViewStyle } from 'react-native';
import { Colors } from '../theme';

interface StatusIndicatorProps {
  type: 'success' | 'error' | 'warning' | 'info';
  size?: 'small' | 'medium' | 'large';
  icon?: React.ReactNode;
  style?: ViewStyle;
}

const StatusIndicator: React.FC<StatusIndicatorProps> = ({
  type,
  size = 'medium',
  icon,
  style,
}) => {
  return (
    <View
      style={[
        styles.container,
        type === 'success' && styles.success,
        type === 'error' && styles.error,
        type === 'warning' && styles.warning,
        type === 'info' && styles.info,
        size === 'small' && styles.small,
        size === 'medium' && styles.medium,
        size === 'large' && styles.large,
        style,
      ]}
    >
      {icon}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    alignItems: 'center',
    justifyContent: 'center',
    borderRadius: 9999, // Round border radius
  },
  success: {
    backgroundColor: Colors.Success,
  },
  error: {
    backgroundColor: Colors.Error,
  },
  warning: {
    backgroundColor: Colors.Warning,
  },
  info: {
    backgroundColor: Colors.Info,
  },
  small: {
    width: 32,
    height: 32,
  },
  medium: {
    width: 48,
    height: 48,
  },
  large: {
    width: 64,
    height: 64,
  },
});

export default StatusIndicator;
