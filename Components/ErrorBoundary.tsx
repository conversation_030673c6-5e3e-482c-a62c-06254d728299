import React, { Component, ReactNode } from 'react';
import { View, Text, StyleSheet, TouchableOpacity } from 'react-native';
import { Colors, Typography, Spacing } from '../theme';

interface Props {
  children: ReactNode;
  fallback?: ReactNode;
  onError?: (error: Error, errorInfo: any) => void;
}

interface State {
  hasError: boolean;
  error?: Error;
}

class ErrorBoundary extends Component<Props, State> {
  constructor(props: Props) {
    super(props);
    this.state = { hasError: false };
  }

  static getDerivedStateFromError(error: Error): State {
    // Update state so the next render will show the fallback UI
    return { hasError: true, error };
  }

  componentDidCatch(error: Error, errorInfo: any) {
    // Log the error to crash reporting service
    console.error('ErrorBoundary caught an error:', error, errorInfo);
    
    // Call custom error handler if provided
    if (this.props.onError) {
      this.props.onError(error, errorInfo);
    }
  }

  handleRetry = () => {
    this.setState({ hasError: false, error: undefined });
  };

  render() {
    if (this.state.hasError) {
      // Custom fallback UI
      if (this.props.fallback) {
        return this.props.fallback;
      }

      // Default fallback UI
      return (
        <View style={styles.container}>
          <View style={styles.content}>
            <Text style={styles.title}>Oops! Something went wrong</Text>
            <Text style={styles.message}>
              We're sorry, but something unexpected happened. Please try again.
            </Text>
            {__DEV__ && this.state.error && (
              <Text style={styles.errorDetails}>
                {this.state.error.toString()}
              </Text>
            )}
            <TouchableOpacity style={styles.retryButton} onPress={this.handleRetry}>
              <Text style={styles.retryButtonText}>Try Again</Text>
            </TouchableOpacity>
          </View>
        </View>
      );
    }

    return this.props.children;
  }
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.BackgroundPrimary,
    justifyContent: 'center',
    alignItems: 'center',
    padding: Spacing.Large,
  },
  content: {
    alignItems: 'center',
    maxWidth: 300,
  },
  title: {
    ...Typography.Heading2,
    color: Colors.DarkText,
    textAlign: 'center',
    marginBottom: Spacing.Medium,
  },
  message: {
    ...Typography.BodyText,
    color: Colors.LightText,
    textAlign: 'center',
    marginBottom: Spacing.Large,
  },
  errorDetails: {
    ...Typography.Caption,
    color: Colors.Error,
    textAlign: 'center',
    marginBottom: Spacing.Large,
    fontFamily: 'monospace',
  },
  retryButton: {
    backgroundColor: Colors.DarkText,
    paddingVertical: Spacing.Medium,
    paddingHorizontal: Spacing.Large,
    borderRadius: 8,
  },
  retryButtonText: {
    ...Typography.ButtonText,
    color: Colors.BackgroundPrimary,
  },
});

export default ErrorBoundary;
