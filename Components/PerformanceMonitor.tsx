import React, { useEffect, useRef, memo } from 'react';
import { InteractionManager, Platform } from 'react-native';

interface PerformanceMonitorProps {
  children: React.ReactNode;
  componentName?: string;
  enableLogging?: boolean;
}

// Performance monitoring hook
export const usePerformanceMonitor = (componentName: string = 'Component') => {
  const mountTime = useRef<number>(Date.now());
  const renderCount = useRef<number>(0);

  useEffect(() => {
    renderCount.current += 1;
    
    if (__DEV__) {
      const currentTime = Date.now();
      const timeSinceMount = currentTime - mountTime.current;
      
      console.log(`[Performance] ${componentName} - Render #${renderCount.current} - Time since mount: ${timeSinceMount}ms`);
    }
  });

  useEffect(() => {
    const startTime = Date.now();
    
    // Measure time to interactive
    InteractionManager.runAfterInteractions(() => {
      const interactiveTime = Date.now() - startTime;
      if (__DEV__) {
        console.log(`[Performance] ${componentName} - Time to interactive: ${interactiveTime}ms`);
      }
    });

    return () => {
      if (__DEV__) {
        const totalMountTime = Date.now() - mountTime.current;
        console.log(`[Performance] ${componentName} - Total mount time: ${totalMountTime}ms - Total renders: ${renderCount.current}`);
      }
    };
  }, [componentName]);

  return {
    renderCount: renderCount.current,
    mountTime: mountTime.current,
  };
};

// Memory monitoring hook
export const useMemoryMonitor = (componentName: string = 'Component') => {
  useEffect(() => {
    if (__DEV__ && Platform.OS === 'ios') {
      // iOS memory monitoring
      const checkMemory = () => {
        // @ts-ignore - This is for development only
        if (global.performance && global.performance.memory) {
          // @ts-ignore
          const memory = global.performance.memory;
          console.log(`[Memory] ${componentName} - Used: ${(memory.usedJSHeapSize / 1024 / 1024).toFixed(2)}MB`);
        }
      };

      const interval = setInterval(checkMemory, 5000);
      return () => clearInterval(interval);
    }
  }, [componentName]);
};

// Animation performance monitoring
export const useAnimationPerformanceMonitor = (animationName: string = 'Animation') => {
  const frameCount = useRef<number>(0);
  const startTime = useRef<number>(0);

  const startMonitoring = () => {
    frameCount.current = 0;
    startTime.current = Date.now();
  };

  const recordFrame = () => {
    frameCount.current += 1;
  };

  const endMonitoring = () => {
    if (__DEV__ && startTime.current > 0) {
      const duration = Date.now() - startTime.current;
      const fps = (frameCount.current / duration) * 1000;
      console.log(`[Animation Performance] ${animationName} - FPS: ${fps.toFixed(2)} - Frames: ${frameCount.current} - Duration: ${duration}ms`);
    }
  };

  return {
    startMonitoring,
    recordFrame,
    endMonitoring,
  };
};

// Performance monitoring component
const PerformanceMonitor: React.FC<PerformanceMonitorProps> = memo(({
  children,
  componentName = 'PerformanceMonitor',
  enableLogging = __DEV__,
}) => {
  usePerformanceMonitor(componentName);
  
  if (enableLogging) {
    useMemoryMonitor(componentName);
  }

  return <>{children}</>;
});

PerformanceMonitor.displayName = 'PerformanceMonitor';

export default PerformanceMonitor;
