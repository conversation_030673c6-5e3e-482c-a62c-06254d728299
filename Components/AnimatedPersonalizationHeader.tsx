import React, { useEffect } from 'react';
import { View, StyleSheet, ViewStyle, SafeAreaView } from 'react-native';
import { Colors, Spacing, Shadow } from '../theme';
import BackButton from './BackButton';
import Typography from './Typography';
import Animated, {
  useSharedValue,
  useAnimatedStyle,
  withTiming,
  withDelay,
  withSequence,
  Easing,
  interpolate,
} from 'react-native-reanimated';
import { AnimationDuration, AnimationEasing } from './animations';

interface AnimatedPersonalizationHeaderProps {
  currentStep: number;
  totalSteps: number;
  onBack: () => void;
  style?: ViewStyle;
  animationDelay?: number;
}

const AnimatedPersonalizationHeader: React.FC<AnimatedPersonalizationHeaderProps> = ({
  currentStep,
  totalSteps,
  onBack,
  style,
  animationDelay = 0,
}) => {
  const progress = Math.min(Math.max(currentStep / totalSteps, 0), 1);

  // Animation values
  const headerOpacity = useSharedValue(0);
  const progressWidth = useSharedValue(0);

  useEffect(() => {
    // Animate header
    headerOpacity.value = withDelay(
      animationDelay,
      withTiming(1, {
        duration: AnimationDuration.Medium,
        easing: AnimationEasing.Smooth
      })
    );

    // Animate progress bar with a slight delay
    progressWidth.value = withDelay(
      animationDelay + 300,
      withTiming(progress, {
        duration: AnimationDuration.Long,
        easing: AnimationEasing.Bounce
      })
    );

    return () => {
      // Cleanup
      headerOpacity.value = 0;
      progressWidth.value = 0;
    };
  }, []);

  // Update progress when currentStep changes
  useEffect(() => {
    progressWidth.value = withTiming(progress, {
      duration: AnimationDuration.Medium,
      easing: AnimationEasing.Bounce
    });
  }, [currentStep, totalSteps]);

  // Animated styles
  const headerAnimatedStyle = useAnimatedStyle(() => ({
    opacity: headerOpacity.value,
    transform: [
      { translateY: interpolate(headerOpacity.value, [0, 1], [-10, 0]) }
    ]
  }));

  const progressAnimatedStyle = useAnimatedStyle(() => ({
    width: `${progressWidth.value * 100}%`,
    opacity: interpolate(headerOpacity.value, [0, 1], [0.5, 1])
  }));

  return (
    <SafeAreaView style={styles.safeArea}>
      <Animated.View style={[styles.header, Shadow.Small, headerAnimatedStyle, style]}>
        <View style={styles.topRow}>
          <View style={styles.backButtonContainer}>
            <BackButton
              onPress={onBack}
              style={styles.backButton}
            />
          </View>
          <Typography variant="description" style={styles.stepText}>
            Step {currentStep} of {totalSteps}
          </Typography>
        </View>
        <View style={styles.progressBarContainer}>
          <View style={styles.progressBarTrack}>
            <Animated.View
              style={[
                styles.progressBarFill,
                progressAnimatedStyle
              ]}
            />
          </View>
        </View>
      </Animated.View>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  safeArea: {
    backgroundColor: Colors.BackgroundPrimary,
  },
  header: {
    paddingHorizontal: Spacing.Large,
    paddingTop: Spacing.Medium,
    paddingBottom: Spacing.Medium,
    backgroundColor: Colors.BackgroundPrimary,
    borderBottomWidth: 1,
    borderBottomColor: Colors.SurfaceSecondary,
  },
  topRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: Spacing.Medium,
  },
  backButtonContainer: {
    // No additional styling needed
  },
  backButton: {
    backgroundColor: Colors.SurfaceSecondary,
    borderRadius: 9999, // Round border radius
    padding: Spacing.Small,
  },
  stepText: {
    color: Colors.DarkText,
    fontWeight: '500', // Make it slightly bolder for emphasis
  },
  progressBarContainer: {
    width: '100%',
  },
  progressBarTrack: {
    height: 8, // Taller for better visibility
    backgroundColor: Colors.SurfaceSecondary,
    borderRadius: 8, // Small border radius
    overflow: 'hidden',
  },
  progressBarFill: {
    height: '100%',
    backgroundColor: Colors.DarkText,
    borderRadius: 8, // Small border radius
  },
});

export default AnimatedPersonalizationHeader;
