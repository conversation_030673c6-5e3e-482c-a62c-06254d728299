import React from 'react';
import {
  View,
  TextInput,
  StyleSheet,
  ViewStyle,
  TextInputProps,
} from 'react-native';
import { Colors, Typography, Spacing } from '../theme';
import TypographyComponent from './Typography';

interface InputProps extends TextInputProps {
  label?: string;
  error?: string;
  containerStyle?: ViewStyle;
  leftIcon?: React.ReactNode;
  rightIcon?: React.ReactNode;
}

const Input: React.FC<InputProps> = ({
  label,
  error,
  containerStyle,
  leftIcon,
  rightIcon,
  style,
  ...props
}) => {
  return (
    <View style={[styles.container, containerStyle]}>
      {label && (
        <TypographyComponent variant="description" style={styles.label}>
          {label}
        </TypographyComponent>
      )}
      <View style={[styles.inputContainer, error && styles.inputError]}>
        {leftIcon && <View style={styles.leftIcon}>{leftIcon}</View>}
        <TextInput
          style={[styles.input, style]}
          placeholderTextColor={Colors.LightText}
          {...props}
        />
        {rightIcon && <View style={styles.rightIcon}>{rightIcon}</View>}
      </View>
      {error && (
        <TypographyComponent
          variant="description"
          style={styles.errorText}
          color={Colors.Error}
        >
          {error}
        </TypographyComponent>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    width: '100%',
    marginBottom: Spacing.Medium,
  },
  label: {
    marginBottom: Spacing.Small,
  },
  inputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: Colors.SurfaceSecondary,
    borderRadius: 8, // Small border radius
    paddingHorizontal: Spacing.Medium,
    paddingVertical: Spacing.Medium,
  },
  inputError: {
    borderWidth: 1,
    borderColor: Colors.Error,
  },
  input: {
    flex: 1,
    ...Typography.BodyText,
    padding: 0,
  },
  leftIcon: {
    marginRight: Spacing.Small,
  },
  rightIcon: {
    marginLeft: Spacing.Small,
  },
  errorText: {
    marginTop: Spacing.Small,
  },
});

export default Input;
