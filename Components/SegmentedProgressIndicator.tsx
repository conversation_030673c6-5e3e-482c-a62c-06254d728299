import React, { useEffect, useState } from 'react';
import { View, StyleSheet, Animated, Easing } from 'react-native';
import { Svg, Circle, Defs, RadialGradient, Stop, LinearGradient } from 'react-native-svg';
import { Colors, Spacing } from '../theme';
import Typography from './Typography';
import { Ionicons } from '@expo/vector-icons';
import { getAnimationSettings } from '../src/utils/refreshRate';

interface SegmentedProgressIndicatorProps {
  size?: number;
  strokeWidth?: number;
  duration?: number;
  segments?: number;
  currentSegment?: number;
  showText?: boolean;
  textContent?: string[];
}

const SegmentedProgressIndicator: React.FC<SegmentedProgressIndicatorProps> = ({
  size = 200,
  strokeWidth = 8,
  duration = 7000, // 7 seconds
  segments = 3,
  currentSegment = 0,
  showText = false,
  textContent = ['Analyzing Product Image', 'Processing Ingredients', 'Generating Results'],
}) => {
  const [progress, setProgress] = useState(0);
  const [animatedValue] = useState(new Animated.Value(0));
  const [rotationValue] = useState(new Animated.Value(0));
  const [glowValue] = useState(new Animated.Value(0));

  const radius = (size - strokeWidth) / 2;
  const circumference = radius * 2 * Math.PI;

  // Get high refresh rate animation settings
  const animationSettings = getAnimationSettings();

  useEffect(() => {
    // Start the rotation animation with high refresh rate optimization
    Animated.loop(
      Animated.timing(rotationValue, {
        toValue: 1,
        duration: 3000,
        easing: Easing.linear,
        useNativeDriver: true,
        isInteraction: false,
      })
    ).start();

    // Start the glow animation with smooth easing
    Animated.loop(
      Animated.sequence([
        Animated.timing(glowValue, {
          toValue: 1,
          duration: 1500,
          easing: Easing.bezier(0.4, 0.0, 0.2, 1), // Material Design easing
          useNativeDriver: true,
          isInteraction: false,
        }),
        Animated.timing(glowValue, {
          toValue: 0,
          duration: 1500,
          easing: Easing.bezier(0.4, 0.0, 0.2, 1),
          useNativeDriver: true,
          isInteraction: false,
        }),
      ])
    ).start();

    // Start the main animation with optimized settings
    Animated.timing(animatedValue, {
      toValue: 1,
      duration: duration / segments,
      easing: Easing.bezier(0.25, 0.1, 0.25, 1), // Smooth cubic-bezier
      useNativeDriver: true,
      isInteraction: false,
    }).start();

    // Update progress dynamically with high refresh rate optimization
    const interval = setInterval(() => {
      setProgress((prev) => {
        const segmentSize = 100 / segments;
        const currentSegmentStart = currentSegment * segmentSize;
        const currentSegmentEnd = (currentSegment + 1) * segmentSize;

        // Calculate progress within the current segment with optimized increments
        const segmentProgress = (prev - currentSegmentStart) / segmentSize;
        const increment = animationSettings.increment; // Use optimized increment
        const targetProgress = currentSegmentStart + (segmentSize * Math.min(segmentProgress + increment, 1));

        return Math.min(targetProgress, currentSegmentEnd);
      });
    }, animationSettings.frameTiming); // Use optimized frame timing

    return () => clearInterval(interval);
  }, [animatedValue, rotationValue, glowValue, duration, segments, currentSegment]);

  // Calculate the stroke dashoffset based on progress
  const strokeDashoffset = circumference - (progress / 100) * circumference;

  // Animation interpolations
  const rotation = rotationValue.interpolate({
    inputRange: [0, 1],
    outputRange: ['0deg', '360deg'],
  });

  const glowOpacity = glowValue.interpolate({
    inputRange: [0, 1],
    outputRange: [0.3, 0.8],
  });

  return (
    <View style={styles.container}>
      {/* Glow effect background */}
      <Animated.View style={[styles.glowContainer, { opacity: glowOpacity }]}>
        <View style={[styles.glowRing, { width: size + 20, height: size + 20 }]} />
      </Animated.View>

      <Svg width={size} height={size} viewBox={`0 0 ${size} ${size}`}>
        <Defs>
          {/* Gradient for progress ring */}
          <LinearGradient id="progressGradient" x1="0%" y1="0%" x2="100%" y2="100%">
            <Stop offset="0%" stopColor="#FFFFFF" stopOpacity="1" />
            <Stop offset="50%" stopColor="#F8F9FA" stopOpacity="1" />
            <Stop offset="100%" stopColor="#E9ECEF" stopOpacity="1" />
          </LinearGradient>

          {/* Gradient for orb outer */}
          <RadialGradient id="orbOuterGradient" cx="30%" cy="30%">
            <Stop offset="0%" stopColor="#F8F9FA" stopOpacity="1" />
            <Stop offset="40%" stopColor="#E9ECEF" stopOpacity="1" />
            <Stop offset="70%" stopColor="#DEE2E6" stopOpacity="1" />
            <Stop offset="100%" stopColor="#CED4DA" stopOpacity="1" />
          </RadialGradient>

          {/* Gradient for orb inner */}
          <RadialGradient id="orbInnerGradient" cx="40%" cy="40%">
            <Stop offset="0%" stopColor="#FFFFFF" stopOpacity="1" />
            <Stop offset="60%" stopColor="#F1F3F4" stopOpacity="1" />
            <Stop offset="100%" stopColor="#E8EAED" stopOpacity="1" />
          </RadialGradient>
        </Defs>

        {/* Background circle */}
        <Circle
          cx={size / 2}
          cy={size / 2}
          r={radius}
          stroke="#F0F0F0"
          strokeWidth={strokeWidth}
          fill="none"
          opacity={0.3}
        />

        {/* Progress circle */}
        <Circle
          cx={size / 2}
          cy={size / 2}
          r={radius}
          stroke="url(#progressGradient)"
          strokeWidth={strokeWidth}
          strokeDasharray={`${circumference}`}
          strokeDashoffset={strokeDashoffset}
          strokeLinecap="round"
          fill="none"
          transform={`rotate(-90, ${size / 2}, ${size / 2})`}
        />
      </Svg>

      {/* Central orb icon */}
      <Animated.View style={[styles.iconContainer, { transform: [{ rotate: rotation }] }]}>
        <View style={styles.orbOuter}>
          <View style={styles.orbInner}>
            <View style={styles.lensContainer}>
              <View style={styles.lensOuter}>
                <View style={styles.lensInner}>
                  <View style={styles.lensCenter} />
                  <View style={styles.lensReflection} />
                </View>
              </View>
              <View style={styles.lensRing} />
            </View>
          </View>
        </View>
      </Animated.View>

      {showText && (
        <View style={styles.textContainer}>
          <Typography variant="description" style={styles.text}>
            {textContent[currentSegment] || textContent[textContent.length - 1]}
          </Typography>
        </View>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    alignItems: 'center',
    justifyContent: 'center',
  },
  glowContainer: {
    position: 'absolute',
    alignItems: 'center',
    justifyContent: 'center',
  },
  glowRing: {
    borderRadius: 1000,
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    shadowColor: '#FFFFFF',
    shadowOffset: { width: 0, height: 0 },
    shadowOpacity: 0.3,
    shadowRadius: 15,
    elevation: 10,
  },
  iconContainer: {
    position: 'absolute',
    alignItems: 'center',
    justifyContent: 'center',
  },
  orbOuter: {
    width: 100,
    height: 100,
    borderRadius: 50,
    backgroundColor: 'rgba(255, 255, 255, 0.9)',
    alignItems: 'center',
    justifyContent: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 8 },
    shadowOpacity: 0.15,
    shadowRadius: 12,
    elevation: 8,
    borderWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.3)',
  },
  orbInner: {
    width: 80,
    height: 80,
    borderRadius: 40,
    backgroundColor: 'rgba(255, 255, 255, 0.8)',
    alignItems: 'center',
    justifyContent: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: -2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 4,
    borderWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.4)',
  },
  lensContainer: {
    width: 50,
    height: 50,
    alignItems: 'center',
    justifyContent: 'center',
  },
  lensOuter: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: '#2C2C2C',
    alignItems: 'center',
    justifyContent: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.3,
    shadowRadius: 4,
    elevation: 5,
  },
  lensInner: {
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: '#1A1A1A',
    alignItems: 'center',
    justifyContent: 'center',
    position: 'relative',
  },
  lensCenter: {
    width: 20,
    height: 20,
    borderRadius: 10,
    backgroundColor: '#FFB300',
    shadowColor: '#FFB300',
    shadowOffset: { width: 0, height: 0 },
    shadowOpacity: 0.5,
    shadowRadius: 3,
    elevation: 3,
  },
  lensReflection: {
    position: 'absolute',
    top: 4,
    left: 6,
    width: 8,
    height: 8,
    borderRadius: 4,
    backgroundColor: 'rgba(255, 255, 255, 0.4)',
  },
  lensRing: {
    position: 'absolute',
    width: 46,
    height: 46,
    borderRadius: 23,
    borderWidth: 2,
    borderColor: '#666',
  },
  textContainer: {
    position: 'absolute',
    alignItems: 'center',
    justifyContent: 'center',
    padding: Spacing.Small,
  },
  text: {
    textAlign: 'center',
    color: Colors.DarkText,
  },
});

export default SegmentedProgressIndicator;
