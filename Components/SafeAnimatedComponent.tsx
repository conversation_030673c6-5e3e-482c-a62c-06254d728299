import React, { Component, ReactNode } from 'react';
import { View, StyleSheet } from 'react-native';
import { runOnJS, useAnimatedStyle } from 'react-native-reanimated';

interface Props {
  children: ReactNode;
  fallback?: ReactNode;
  onError?: (error: Error) => void;
}

interface State {
  hasError: boolean;
  error?: Error;
}

/**
 * SafeAnimatedComponent - Error boundary specifically for React Native Reanimated
 * Prevents SIGABRT crashes by catching worklet errors and providing fallbacks
 */
class SafeAnimatedComponent extends Component<Props, State> {
  constructor(props: Props) {
    super(props);
    this.state = { hasError: false };
  }

  static getDerivedStateFromError(error: Error): State {
    // Check if it's a Reanimated-related error
    const isReanimatedError = error.message?.includes('worklet') ||
                             error.message?.includes('Reanimated') ||
                             error.stack?.includes('reanimated');
    
    if (isReanimatedError) {
      console.warn('SafeAnimatedComponent: Caught Reanimated error:', error);
    }
    
    return { hasError: true, error };
  }

  componentDidCatch(error: Error, errorInfo: any) {
    // Log the error for debugging
    console.error('SafeAnimatedComponent caught an error:', error, errorInfo);
    
    // Call custom error handler if provided
    if (this.props.onError) {
      this.props.onError(error);
    }
  }

  handleRetry = () => {
    this.setState({ hasError: false, error: undefined });
  };

  render() {
    if (this.state.hasError) {
      // Return fallback UI or null to prevent crashes
      if (this.props.fallback) {
        return this.props.fallback;
      }
      
      // Default fallback - just render a simple view
      return <View style={styles.fallbackContainer} />;
    }

    return this.props.children;
  }
}

/**
 * Higher-order component to wrap components with animation error handling
 */
export const withSafeAnimation = <P extends object>(
  WrappedComponent: React.ComponentType<P>
) => {
  return React.forwardRef<any, P>((props, ref) => (
    <SafeAnimatedComponent>
      <WrappedComponent {...props} ref={ref} />
    </SafeAnimatedComponent>
  ));
};

/**
 * Safe worklet wrapper that catches errors in UI thread operations
 */
export const safeWorklet = (workletFn: () => void, fallback?: () => void) => {
  'worklet';
  try {
    workletFn();
  } catch (error) {
    console.warn('SafeWorklet: Error in worklet:', error);
    if (fallback) {
      try {
        fallback();
      } catch (fallbackError) {
        console.error('SafeWorklet: Error in fallback:', fallbackError);
      }
    }
    // Use runOnJS to report error to JS thread safely
    runOnJS(() => {
      console.error('Worklet error reported to JS thread:', error);
    })();
  }
};

/**
 * Safe animated style hook that provides error handling
 */
export const useSafeAnimatedStyle = (
  styleFunction: () => any,
  dependencies?: any[]
) => {
  const safeStyleFunction = () => {
    'worklet';
    try {
      return styleFunction();
    } catch (error) {
      console.warn('SafeAnimatedStyle: Error in style function:', error);
      // Return empty style object as fallback
      return {};
    }
  };

  // Wrap with Reanimated hook so the returned value is a proper animated style object
  // This fixes TS type errors when the style is consumed in component arrays.
  // eslint-disable-next-line react-hooks/rules-of-hooks
  return useAnimatedStyle(safeStyleFunction, dependencies);
};

/**
 * Memory pressure detection for animations
 */
export const checkMemoryPressure = (): boolean => {
  // Simple heuristic - in production, you might want to use a proper memory monitoring library
  try {
    const memoryInfo = (global as any).performance?.memory;
    if (memoryInfo) {
      const usedMemory = memoryInfo.usedJSHeapSize;
      const totalMemory = memoryInfo.totalJSHeapSize;
      const memoryUsageRatio = usedMemory / totalMemory;
      
      // If memory usage is above 80%, consider it high pressure
      return memoryUsageRatio > 0.8;
    }
  } catch (error) {
    console.warn('Memory pressure check failed:', error);
  }
  
  return false;
};

/**
 * Safe animation configuration that adapts to memory pressure
 */
export const getSafeAnimationConfig = (duration: number = 300) => {
  const isHighMemoryPressure = checkMemoryPressure();
  
  return {
    duration: isHighMemoryPressure ? duration * 0.5 : duration, // Faster animations under pressure
    useNativeDriver: true,
    isInteraction: false,
  };
};

const styles = StyleSheet.create({
  fallbackContainer: {
    // Minimal fallback styling
    backgroundColor: 'transparent',
  },
});

export default SafeAnimatedComponent;
