import React from 'react';
import { TouchableOpacity, StyleSheet, ViewStyle } from 'react-native';
import { Colors, Spacing } from '../theme';
import Typography from './Typography';
import { Ionicons } from '@expo/vector-icons';

interface TagProps {
  label: string;
  onPress?: () => void;
  onRemove?: () => void;
  style?: ViewStyle;
  selected?: boolean;
}

const Tag: React.FC<TagProps> = ({
  label,
  onPress,
  onRemove,
  style,
  selected = false,
}) => {
  return (
    <TouchableOpacity
      style={[
        styles.container,
        selected ? styles.selectedContainer : styles.unselectedContainer,
        style,
      ]}
      onPress={onPress}
      activeOpacity={onPress ? 0.7 : 1}
      disabled={!onPress}
    >
      <Typography
        variant="description"
        color={selected ? Colors.BackgroundPrimary : Colors.DarkText}
      >
        {label}
      </Typography>
      {onRemove && (
        <TouchableOpacity
          style={styles.removeButton}
          onPress={onRemove}
          hitSlop={{ top: 10, right: 10, bottom: 10, left: 10 }}
        >
          <Ionicons
            name="close-circle"
            size={16}
            color={selected ? Colors.BackgroundPrimary : Colors.LightText}
          />
        </TouchableOpacity>
      )}
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: Spacing.Small,
    paddingHorizontal: Spacing.Medium,
    borderRadius: 9999, // Round border radius
    marginRight: Spacing.Small,
    marginBottom: Spacing.Small,
  },
  selectedContainer: {
    backgroundColor: Colors.DarkText,
  },
  unselectedContainer: {
    backgroundColor: Colors.SurfaceSecondary,
  },
  removeButton: {
    marginLeft: Spacing.Small,
  },
});

export default Tag;
