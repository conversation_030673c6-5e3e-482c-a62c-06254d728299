import React, { memo } from 'react';
import { Text, StyleSheet, TextStyle, TextProps } from 'react-native';
import { Typography as TypographyStyles } from '../theme';

interface TypographyProps extends TextProps {
  variant?: 'heading1' | 'heading2' | 'bodyText' | 'description' | 'caption' | 'buttonText';
  color?: string;
  style?: TextStyle;
  children: React.ReactNode;
}

const Typography: React.FC<TypographyProps> = memo(({
  variant = 'bodyText',
  color,
  style,
  children,
  ...props
}) => {
  return (
    <Text
      style={[
        variant === 'heading1' && styles.heading1,
        variant === 'heading2' && styles.heading2,
        variant === 'bodyText' && styles.bodyText,
        variant === 'description' && styles.description,
        variant === 'caption' && styles.caption,
        variant === 'buttonText' && styles.buttonText,
        color && { color },
        style,
      ]}
      {...props}
    >
      {children}
    </Text>
  );
});

const styles = StyleSheet.create({
  heading1: {
    ...TypographyStyles.Heading1,
  },
  heading2: {
    ...TypographyStyles.Heading2,
  },
  bodyText: {
    ...TypographyStyles.BodyText,
  },
  description: {
    ...TypographyStyles.Description,
  },
  caption: {
    ...TypographyStyles.Caption,
  },
  buttonText: {
    ...TypographyStyles.ButtonText,
  },
});

export default Typography;
