import React from 'react';
import { View, StyleSheet, ViewStyle } from 'react-native';
import { Colors, Spacing } from '../theme';
import Typography from './Typography';

interface ProgressBarProps {
  current: number;
  total: number;
  style?: ViewStyle;
  showText?: boolean;
}

const ProgressBar: React.FC<ProgressBarProps> = ({
  current,
  total,
  style,
  showText = true,
}) => {
  const progress = Math.min(Math.max(current / total, 0), 1);

  return (
    <View style={[styles.container, style]}>
      <View style={styles.progressContainer}>
        <View
          style={[styles.progressBar, { width: `${progress * 100}%` }]}
        />
      </View>
      {showText && (
        <Typography variant="description" style={styles.text}>
          {current}/{total}
        </Typography>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    width: '100%',
  },
  progressContainer: {
    height: 4,
    backgroundColor: Colors.SurfaceSecondary,
    borderRadius: 8, // Small border radius
    overflow: 'hidden',
  },
  progressBar: {
    height: '100%',
    backgroundColor: Colors.DarkText,
  },
  text: {
    marginTop: Spacing.Small,
    textAlign: 'right',
  },
});

export default ProgressBar;
