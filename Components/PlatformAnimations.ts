import { Platform } from 'react-native';
import { Easing } from 'react-native-reanimated';

/**
 * Platform-specific animation configurations to make Android match iOS exactly
 */

// iOS-like easing curves for Android
export const IOSEasing = {
  // iOS default ease-in-out curve
  Default: Easing.bezier(0.25, 0.1, 0.25, 1),
  // iOS spring animation curve
  Spring: Easing.bezier(0.175, 0.885, 0.32, 1.275),
  // iOS smooth transition curve
  Smooth: Easing.bezier(0.4, 0.0, 0.2, 1),
  // iOS elastic curve for bouncy animations
  Elastic: Easing.bezier(0.68, -0.55, 0.265, 1.55),
  // iOS-like bounce curve (added for compatibility)
  Bounce: Easing.bezier(0.34, 1.56, 0.64, 1),
  // iOS ease-out curve
  EaseOut: Easing.bezier(0.0, 0.0, 0.2, 1),
  // iOS ease-in curve
  EaseIn: Easing.bezier(0.4, 0.0, 1, 1),
};

// Platform-specific animation durations (faster overall)
export const PlatformDuration = {
  // Much faster animations for snappier feel
  Short: Platform.select({
    ios: 120,
    android: 100, // Even faster on Android
  }),
  Medium: Platform.select({
    ios: 200,
    android: 180, // Faster on Android
  }),
  Long: Platform.select({
    ios: 350,
    android: 300, // Faster on Android
  }),
  ExtraLong: Platform.select({
    ios: 500,
    android: 400, // Faster on Android
  }),
};

// Platform-specific spring configurations
export const PlatformSpring = {
  Default: Platform.select({
    ios: {
      damping: 15,
      stiffness: 150,
      mass: 1,
    },
    android: {
      damping: 12, // Slightly less damping for snappier feel
      stiffness: 180, // Higher stiffness for faster response
      mass: 0.8, // Lower mass for quicker animations
    },
  }),
  Bouncy: Platform.select({
    ios: {
      damping: 8,
      stiffness: 100,
      mass: 1,
    },
    android: {
      damping: 6,
      stiffness: 120,
      mass: 0.8,
    },
  }),
  Gentle: Platform.select({
    ios: {
      damping: 20,
      stiffness: 120,
      mass: 1,
    },
    android: {
      damping: 18,
      stiffness: 140,
      mass: 0.9,
    },
  }),
};

// Enhanced shadow configuration for iOS-like shadows on Android
export const PlatformShadow = {
  Small: {},
  Medium: {},
  Large: {},
};

// Platform-specific glow configurations (enhanced for Android visibility)
// Note: shadowOffset removed to prevent animated style errors
export const PlatformGlow = {
  Subtle: Platform.select({
    ios: {
      shadowOpacity: 0.2,
      shadowRadius: 4,
    },
    android: {
      shadowOpacity: 0.4, // Much higher opacity for Android
      shadowRadius: 8, // Larger radius
      elevation: 5, // Higher elevation
    },
  }),
  Medium: Platform.select({
    ios: {
      shadowOpacity: 0.3,
      shadowRadius: 8,
    },
    android: {
      shadowOpacity: 0.5, // Much higher opacity
      shadowRadius: 12, // Larger radius
      elevation: 8, // Higher elevation
    },
  }),
  Strong: Platform.select({
    ios: {
      shadowOpacity: 0.4,
      shadowRadius: 12,
    },
    android: {
      shadowOpacity: 0.6, // Much higher opacity
      shadowRadius: 18, // Much larger radius
      elevation: 12, // Higher elevation
    },
  }),
};

// Platform-specific scale factors for button animations
export const PlatformScale = {
  Button: Platform.select({
    ios: 0.95,
    android: 0.92, // Slightly more pronounced on Android
  }),
  Card: Platform.select({
    ios: 0.98,
    android: 0.96,
  }),
  Icon: Platform.select({
    ios: 0.9,
    android: 0.85,
  }),
};

// Platform-specific opacity configurations
export const PlatformOpacity = {
  Pressed: Platform.select({
    ios: 0.8,
    android: 0.75, // Slightly more transparent on Android
  }),
  Disabled: Platform.select({
    ios: 0.6,
    android: 0.55,
  }),
  Overlay: Platform.select({
    ios: 0.5,
    android: 0.45,
  }),
};

export default {
  IOSEasing,
  PlatformDuration,
  PlatformSpring,
  PlatformShadow,
  PlatformGlow,
  PlatformScale,
  PlatformOpacity,
};
