import React from 'react';
import { View, StyleSheet, ViewStyle } from 'react-native';
import { Colors, Shadow, Spacing } from '../theme';

interface CardProps {
  children: React.ReactNode;
  style?: ViewStyle;
  variant?: 'primary' | 'secondary';
  shadow?: 'none' | 'small' | 'medium' | 'large';
  padding?: 'none' | 'small' | 'medium' | 'large';
}

const Card: React.FC<CardProps> = ({
  children,
  style,
  variant = 'primary',
  shadow = 'small',
  padding = 'medium',
}) => {
  // Get shadow style based on shadow prop
  const getShadowStyle = () => {
    switch (shadow) {
      case 'small':
        return Shadow.Small;
      case 'medium':
        return Shadow.Medium;
      case 'large':
        return Shadow.Large;
      case 'none':
      default:
        return {};
    }
  };

  return (
    <View
      style={[
        styles.card,
        variant === 'primary' && styles.primaryCard,
        variant === 'secondary' && styles.secondaryCard,
        getShadowStyle(),
        padding === 'small' && styles.smallPadding,
        padding === 'medium' && styles.mediumPadding,
        padding === 'large' && styles.largePadding,
        style,
      ]}
    >
      {children}
    </View>
  );
};

const styles = StyleSheet.create({
  card: {
    borderRadius: 12, // Medium border radius
    overflow: 'hidden',
  },
  primaryCard: {
    backgroundColor: Colors.BackgroundPrimary,
  },
  secondaryCard: {
    backgroundColor: Colors.SurfaceSecondary,
  },
  smallPadding: {
    padding: Spacing.Small,
  },
  mediumPadding: {
    padding: Spacing.Medium,
  },
  largePadding: {
    padding: Spacing.Large,
  },
});

export default Card;
