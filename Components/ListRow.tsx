import React from 'react';
import { View, TouchableOpacity, StyleSheet, ViewStyle } from 'react-native';
import { Colors, Spacing } from '../theme';
import Typography from './Typography';

interface ListRowProps {
  title: string;
  description?: string;
  leftIcon?: React.ReactNode;
  rightIcon?: React.ReactNode;
  onPress?: () => void;
  style?: ViewStyle;
  showSeparator?: boolean;
}

const ListRow: React.FC<ListRowProps> = ({
  title,
  description,
  leftIcon,
  rightIcon,
  onPress,
  style,
  showSeparator = true,
}) => {
  const Container = onPress ? TouchableOpacity : View;

  return (
    <View style={[styles.container, style]}>
      <Container
        style={styles.row}
        onPress={onPress}
        disabled={!onPress}
        activeOpacity={0.7}
      >
        {leftIcon && <View style={styles.leftIcon}>{leftIcon}</View>}
        <View style={styles.content}>
          <Typography variant="bodyText">{title}</Typography>
          {description && (
            <Typography variant="description">{description}</Typography>
          )}
        </View>
        {rightIcon && <View style={styles.rightIcon}>{rightIcon}</View>}
      </Container>
      {showSeparator && <View style={styles.separator} />}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    width: '100%',
  },
  row: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: Spacing.Medium,
    paddingHorizontal: Spacing.Large,
  },
  leftIcon: {
    marginRight: Spacing.Medium,
  },
  content: {
    flex: 1,
  },
  rightIcon: {
    marginLeft: Spacing.Medium,
  },
  separator: {
    height: 1,
    backgroundColor: Colors.SurfaceSecondary,
    marginLeft: Spacing.Large,
  },
});

export default ListRow;
