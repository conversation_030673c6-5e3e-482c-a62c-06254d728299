import React, { useState, useRef } from 'react';
import {
  View,
  Modal,
  StyleSheet,
  TouchableOpacity,
  TouchableWithoutFeedback,
  Animated,
  ActivityIndicator,
} from 'react-native';
import { Colors, Spacing, Shadow } from '../theme';
import Typography from './Typography';
import CustomButton from './CustomButton';
import StatusIndicator from './StatusIndicator';
import { Ionicons } from '@expo/vector-icons';

interface ConfirmDeletionModalProps {
  visible: boolean;
  onClose: () => void;
  onDelete: () => void;
}

const ConfirmDeletionModal: React.FC<ConfirmDeletionModalProps> = ({
  visible,
  onClose,
  onDelete,
}) => {
  const [isDeleting, setIsDeleting] = useState(false);
  const [holdProgress, setHoldProgress] = useState(0);
  const holdTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const progressAnimation = useRef(new Animated.Value(0)).current;

  // Handle hold to delete
  const handleHoldStart = () => {
    setIsDeleting(true);

    // Animate progress
    Animated.timing(progressAnimation, {
      toValue: 1,
      duration: 2000, // 2 seconds hold time
      useNativeDriver: false,
    }).start();

    // Set timeout for delete action
    holdTimeoutRef.current = setTimeout(() => {
      setIsDeleting(false);
      onDelete();
      resetHoldState();
    }, 2000);
  };

  const handleHoldEnd = () => {
    if (holdTimeoutRef.current) {
      clearTimeout(holdTimeoutRef.current);
      holdTimeoutRef.current = null;
    }
    resetHoldState();
  };

  const resetHoldState = () => {
    setIsDeleting(false);
    setHoldProgress(0);
    progressAnimation.setValue(0);
  };

  // Update progress value listener
  React.useEffect(() => {
    const listener = progressAnimation.addListener(({ value }) => {
      setHoldProgress(value);
    });

    return () => {
      progressAnimation.removeListener(listener);
      if (holdTimeoutRef.current) {
        clearTimeout(holdTimeoutRef.current);
      }
    };
  }, [progressAnimation]);

  return (
    <Modal
      visible={visible}
      transparent
      animationType="fade"
      onRequestClose={onClose}
    >
      <TouchableWithoutFeedback onPress={onClose}>
        <View style={styles.overlay}>
          <TouchableWithoutFeedback>
            <View style={[styles.modalContainer, Shadow.Large]}>
              <TouchableOpacity style={styles.closeButton} onPress={onClose}>
                <Ionicons name="close" size={24} color={Colors.DarkText} />
              </TouchableOpacity>

              <StatusIndicator
                type="error"
                size="large"
                icon={<Ionicons name="trash" size={32} color={Colors.BackgroundPrimary} />}
                style={styles.statusIndicator}
              />

              <Typography variant="heading2" style={styles.title}>
                Confirm Deletion
              </Typography>

              <Typography variant="bodyText" style={styles.description}>
                This action will permanently delete all your account data, including scan history and preferences. This action cannot be undone.
              </Typography>

              <View style={styles.buttonContainer}>
                <TouchableOpacity
                  style={[
                    styles.deleteButton,
                    isDeleting && styles.deletingButton,
                  ]}
                  onPressIn={handleHoldStart}
                  onPressOut={handleHoldEnd}
                  activeOpacity={0.8}
                >
                  {isDeleting ? (
                    <View style={styles.deletingContainer}>
                      <ActivityIndicator color={Colors.BackgroundPrimary} size="small" />
                      <Typography
                        variant="buttonText"
                        color={Colors.BackgroundPrimary}
                        style={styles.deletingText}
                      >
                        Hold to Delete ({Math.round(holdProgress * 100)}%)
                      </Typography>
                    </View>
                  ) : (
                    <Typography
                      variant="buttonText"
                      color={Colors.BackgroundPrimary}
                    >
                      Hold to Delete
                    </Typography>
                  )}
                </TouchableOpacity>

                <CustomButton
                  title="Cancel"
                  onPress={onClose}
                  variant="secondary"
                  style={styles.cancelButton}
                />
              </View>
            </View>
          </TouchableWithoutFeedback>
        </View>
      </TouchableWithoutFeedback>
    </Modal>
  );
};

const styles = StyleSheet.create({
  overlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'flex-end',
  },
  modalContainer: {
    backgroundColor: Colors.BackgroundPrimary,
    borderTopLeftRadius: 16, // Large border radius
    borderTopRightRadius: 16, // Large border radius
    padding: Spacing.Large,
  },
  closeButton: {
    position: 'absolute',
    top: Spacing.Medium,
    right: Spacing.Medium,
    padding: Spacing.Small,
    zIndex: 1,
  },
  statusIndicator: {
    alignSelf: 'center',
    marginBottom: Spacing.Large,
  },
  title: {
    textAlign: 'center',
    marginBottom: Spacing.Medium,
  },
  description: {
    textAlign: 'center',
    marginBottom: Spacing.Large,
    color: Colors.LightText,
  },
  buttonContainer: {
    marginTop: Spacing.Medium,
  },
  deleteButton: {
    backgroundColor: Colors.Error,
    paddingVertical: Spacing.Medium,
    paddingHorizontal: Spacing.Large,
    borderRadius: 12, // Medium border radius
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: Spacing.Medium,
  },
  deletingButton: {
    opacity: 0.8,
  },
  deletingContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
  },
  deletingText: {
    marginLeft: Spacing.Small,
  },
  cancelButton: {
    width: '100%',
  },
});

export default ConfirmDeletionModal;
