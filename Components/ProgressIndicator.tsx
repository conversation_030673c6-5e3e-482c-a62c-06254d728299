import React from 'react';
import { View, ActivityIndicator, StyleSheet, ViewStyle } from 'react-native';
import { Colors, Spacing } from '../theme';
import Typography from './Typography';

interface ProgressIndicatorProps {
  size?: 'small' | 'large';
  color?: string;
  text?: string;
  style?: ViewStyle;
}

const ProgressIndicator: React.FC<ProgressIndicatorProps> = ({
  size = 'large',
  color = Colors.DarkText,
  text,
  style,
}) => {
  return (
    <View style={[styles.container, style]}>
      <ActivityIndicator size={size} color={color} />
      {text && (
        <Typography variant="description" style={styles.text}>
          {text}
        </Typography>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    alignItems: 'center',
    justifyContent: 'center',
  },
  text: {
    marginTop: Spacing.Medium,
    textAlign: 'center',
  },
});

export default ProgressIndicator;
