import { Platform } from 'react-native';
import { Easing } from 'react-native-reanimated';
import { NativeStackNavigationOptions } from '@react-navigation/native-stack';

// Enhanced screen options for native stack navigator with iOS-like animations
export const getScreenOptions = (animationType = 'default'): NativeStackNavigationOptions => {
  // Base options that work with NativeStackNavigator
  const baseOptions: NativeStackNavigationOptions = {
    headerShown: false,
    animation: 'default',
    // Enhanced animation timing for smoother transitions
    animationDuration: Platform.select({
      ios: 250, // Faster transitions
      android: 200, // Even faster on Android
    }),
  };

  // Different animation types with platform-specific optimizations
  switch (animationType) {
    case 'fade':
      return {
        ...baseOptions,
        animation: 'fade',
        animationDuration: Platform.select({
          ios: 180, // Much faster fade
          android: 150,
        }),
      };
    case 'slide_from_right':
      return {
        ...baseOptions,
        animation: 'default', // 'default' is slide from right in iOS
        animationDuration: Platform.select({
          ios: 250, // Faster slide
          android: 200,
        }),
      };
    case 'slide_from_bottom':
      return {
        ...baseOptions,
        animation: 'slide_from_bottom',
        presentation: 'modal',
        animationDuration: Platform.select({
          ios: 280, // Faster modal
          android: 220,
        }),
      };
    case 'slide_from_left':
      return {
        ...baseOptions,
        animation: 'slide_from_left',
        animationDuration: Platform.select({
          ios: 250, // Faster slide
          android: 200,
        }),
      };
    case 'none':
      return {
        ...baseOptions,
        animation: 'none',
      };
    case 'ios_like':
      // Special iOS-like animation for Android
      return {
        ...baseOptions,
        animation: Platform.select({
          ios: 'default',
          android: 'slide_from_right',
        }),
        animationDuration: Platform.select({
          ios: 250, // Faster iOS-like animation
          android: 200,
        }),
        gestureEnabled: true,
        gestureDirection: 'horizontal',
      };
    default:
      return baseOptions;
  }
};
