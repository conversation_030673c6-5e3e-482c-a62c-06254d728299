import React from 'react';
import { View, StyleSheet, ViewStyle, StyleProp } from 'react-native';
import { Colors } from '../theme';
import GradeIndicator from './GradeIndicator';
import GlowingElement from './GlowingElement';

interface GlowingGradeIndicatorProps {
  grade: 'A' | 'B' | 'C' | 'D' | 'E';
  size?: 'small' | 'medium' | 'large';
  style?: StyleProp<ViewStyle>;
  glowIntensity?: number;
}

const GlowingGradeIndicator: React.FC<GlowingGradeIndicatorProps> = ({
  grade,
  size = 'medium',
  style,
  glowIntensity = 1.5,
}) => {
  // Get the appropriate color for the grade
  const getGradeColor = () => {
    switch (grade) {
      case 'A': return Colors.GradeA;
      case 'B': return Colors.GradeB;
      case 'C': return Colors.GradeC;
      case 'D': return Colors.GradeD;
      case 'E': return Colors.GradeE;
      default: return Colors.GradeA;
    }
  };

  // Get the appropriate size for the glow effect
  const getGlowSize = () => {
    switch (size) {
      case 'small': return 40;
      case 'large': return 120;
      case 'medium':
      default: return 80;
    }
  };

  return (
    <GlowingElement
      color={getGradeColor()}
      size={getGlowSize()}
      intensity={glowIntensity}
      active={true}
      style={StyleSheet.flatten([styles.container, style])}
    >
      <GradeIndicator
        grade={grade}
        size={size}
        style={styles.gradeIndicator}
      />
    </GlowingElement>
  );
};

const styles = StyleSheet.create({
  container: {
    // No additional styling needed
  },
  gradeIndicator: {
    // No additional styling needed
  },
});

export default GlowingGradeIndicator;
