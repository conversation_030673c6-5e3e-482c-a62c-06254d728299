JunkChk is a smart health companion available on iOS, Android, and the web. It allows users to take photos of packaged food and cosmetic products—specifically the section showing ingredients. Using AI and internal logic, JunkChk analyzes the image and assigns a grade from A to E based on how healthy or safe the product is. It clearly explains why a product received its grade through a short summary and bullet points, breaking down both the good and the bad. This applies to all kinds of items, from snacks and beverages to shampoos and lipsticks.
During onboarding, users can specify their allergies, and JunkChk will flag any ingredients that may be unsafe or trigger allergic reactions—even if the product is vegan or marketed as healthy. All scanned items are saved to the user’s history, and the app allows for side-by-side product comparisons. It supports multiple languages (primarily English) and requires an internet connection, as analysis happens through AI and APIs. JunkChk follows a freemium model, offering essential features for free with ads, and unlocking an ad-free experience through a subscription. Social features are also in the works to enhance user engagement.
