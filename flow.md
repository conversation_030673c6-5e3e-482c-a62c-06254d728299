# JunkChk User Flow & Screen Guide (Hook First with Personalization)

This document outlines the key user flows and screens for the JunkChk application, designed to onboard users smoothly by demonstrating core value upfront and providing early personalization. The visual design strictly follows the JunkChk Design System and the aesthetic of the provided World App screenshots.

## Reference

- **Design System:** Refer to the [JunkChk Design System](link_to_your_design_system.md) for detailed specifications on colors, typography, spacing, components, and icons.
- **Visual Inspiration:** The screens below are designed to mirror the aesthetic of the provided World App screenshots (labeled S1 through S12).

## Core Philosophy

- **Personalize Early, Demonstrate Value Quickly:** Engage the user with relevant questions upfront to tailor the experience and highlight relevant information (like allergens), then get them to the core scanning action and results as quickly as possible. [1, 2, 3, 4, 14]
- **Show, Don't Tell (or Tell Briefly, Then Show):** Explain core functionality concisely before letting the user experience it. [3]
- **Value Before Commitment:** Allow core functionality (scanning, getting grade) without requiring sign-up. [4]
- **Incentivize Account Creation:** Clearly articulate the _benefits_ of signing up (saving history, allergies, comparison, ad-free) _after_ the user has experienced the core value.
- **Minimalist & Focused:** Each screen has a clear purpose. Avoid unnecessary complexity. [3, 7]

## User Flow: Initial Onboarding (Personalization & Anonymous Scan)

This flow takes a brand new user from app launch through a short personalization sequence to their first scanned result without needing an account.

1.  **App Launch**

    - **Screen:** Welcome Screen
    - **Purpose:** Greet the user and briefly introduce JunkChk.
    - **Appearance:** Mirrors World App **S2** structure. JunkChk logo, "JunkChk" title (`Typography.Heading1`), tagline (`Typography.BodyText`). _Initially hides the account creation/login buttons._ Replaces the lower section with a clear call to action towards the personalized onboarding.
    - **Key Elements:** JunkChk Logo, App Title, Tagline, Primary Button: "Get Started".
    - **Interactions & Navigation:**
      - Tap "Get Started": Navigates to the `Start Personalization Screen`.

2.  **Start Personalization**

    - **Screen:** Start Personalization Screen
    - **Purpose:** Introduce the short personalization sequence and explain its benefit – tailoring results and highlighting relevant information. [2, 3]
    - **Appearance:** New screen structure, following Design System. Title ("Let's Personalize Your JunkChk", `Typography.Heading1`), Description ("A few quick questions will help us tailor your experience and highlight what matters most to you.", `Typography.BodyText`).
    - **Key Elements:** Title, Description, Primary Button: "Continue", Secondary Button: "Skip Personalization".
    - **Interactions & Navigation:**
      - Tap "Continue": Navigates to the `Your Goals Screen`.
      - Tap "Skip Personalization": Navigates directly to the `How JunkChk Works Screen` (skipping the personalization questions). [3]

3.  **Your Goals with JunkChk**

    - **Screen:** Your Goals Screen
    - **Purpose:** Understand the user's primary motivation for using the app to potentially tailor future content or feature highlighting, aligning with their needs. [1, 3, 14]
    - **Appearance:** New screen structure, following Design System. Title ("Why are you using JunkChk?", `Typography.Heading2`). Description ("Select the main reason below:", `Typography.BodyText`). Multiple Choice Questions (MCQ) options presented as distinct buttons or tappable cards. Progress Indicator (e.g., 1/3) at the top. Back arrow. [4, 9]
    - **Key Elements:** Progress Indicator, Back Arrow, Title, Description, MCQ Options (e.g., "To identify allergens/intolerances", "To understand ingredient lists", "To find healthier options", "To track product information", "Other"), Primary Button: "Next" (disabled until an option is selected).
    - **Interactions & Navigation:**
      - Tap Back arrow: Navigates back to the `Start Personalization Screen`.
      - Tap an MCQ Option: Selects the option. Enables the "Next" button.
      - Tap "Next": Navigates to the `Allergies & Restrictions Screen`.

4.  **Tell Us About Allergies/Restrictions**

    - **Screen:** Allergies & Restrictions Screen
    - **Purpose:** Gather critical allergy/restriction information for personalized safety warnings and highlights in scan results. [15, 16, 17]
    - **Appearance:** New screen structure, following Design System. Title ("Any allergies or dietary restrictions?", `Typography.Heading2`). Description ("We'll highlight these in your scan results.", `Typography.BodyText`). Common allergens/restrictions listed as tappable options (like tags or buttons), maybe a search/input field for others. Progress Indicator (e.g., 2/3). Back arrow. [4, 9]
    - **Key Elements:** Progress Indicator, Back Arrow, Title, Description, Tappable Allergy/Restriction Options (e.g., "Gluten", "Dairy", "Peanuts", "Tree Nuts", "Soy", "Shellfish", "Vegan", "Vegetarian", "+ Add Custom"), Optional: Search/Input field. Primary Button: "Next".
    - **Interactions & Navigation:**
      - Tap Back arrow: Navigates back to the `Your Goals Screen`.
      - Tap an Option: Selects/deselects the allergy/restriction. Options could visually indicate selection.
      - Tap "+ Add Custom": Potentially opens a modal or navigates to a simple input screen to type in a custom restriction.
      - Tap "Next": Navigates to the `How Your Answers Help Screen`.

5.  **How Your Answers Help**

    - **Screen:** How Your Answers Help Screen
    - **Purpose:** Briefly explain how the gathered information will be used to provide a better user experience, reinforcing the value of personalization before moving to core functionality. [2]
    - **Appearance:** New screen structure, following Design System. Title ("Great! Here's how we'll use your answers:", `Typography.Heading2`). Description/Bullet points explaining personalized highlights (especially for allergies), tailored insights, etc. Progress Indicator (e.g., 3/3 - Completed). Back arrow. [4, 9]
    - **Key Elements:** Progress Indicator (Completed), Back Arrow, Title, Explanation text/points (e.g., "- Instantly see if a product contains your allergens", "- Get insights relevant to your goals"), Primary Button: "Show me how JunkChk works".
    - **Interactions & Navigation:**
      - Tap Back arrow: Navigates back to the `Allergies & Restrictions Screen`.
      - Tap "Show me how JunkChk works": Navigates to the `How JunkChk Works Screen`.

6.  **Explain Core Functionality**

    - **Screen:** How JunkChk Works Screen
    - **Purpose:** Briefly explain what JunkChk does and prepares the user for the scan. Replaces World App's "Learn about the Orb" (S7).
    - **Appearance:** Similar structure to World App **S7**. Title (`Typography.Heading1`), Description (`Typography.BodyText`). Feature list using `List Row` components with icons explaining the scan process (photo -> analyze -> grade -> breakdown).
    - **Key Elements:** Title ("How it Works" or "Scan Any Product"), Description, Icon/Text List (Camera -> Analyze -> Grade -> Details), Primary Button: "Got It, Let's Scan".
    - **Interactions & Navigation:**
      - Tap "Got It, Let's Scan": Navigates to the `Scan Screen`.

7.  **Perform Scan**

    - **Screen:** Scan Screen
    - **Purpose:** Allow the user to take a picture of the ingredient list.
    - **Appearance:** Mirrors the concept from the implicit camera view needed before World App's Orb interaction (S9 shows the Orb, implying a previous camera step). Camera feed takes up most space. Minimalist overlay controls.
    - **Key Elements:** Camera view, Capture Button (`Icon Button`, Large circle, `Color.DarkText` background, White Camera icon), Optional Flash/Switch Camera Icons (`Icon Button`). _No Back button needed here initially, the flow is linear._
    - **Interactions & Navigation:**
      - Tap Capture Button: Takes photo, navigates to `Processing Screen`.

8.  **Processing Scan**

    - **Screen:** Processing Screen
    - **Purpose:** Indicate that the app is analyzing the image.
    - **Appearance:** Mirrors World App **S3**. White background, centered spinner (`Progress Indicator`), Title ("Analyzing Product", `Typography.Heading1`), Description ("This may take a moment", `Typography.Description`).
    - **Key Elements:** Spinner, Title, Description.
    - **Interactions & Navigation:**
      - No user interaction. Automatically navigates to `Scan Results Screen` upon completion.

9.  **View Scan Results (Anonymous)**
    - **Screen:** Scan Results Screen (Anonymous)
    - **Purpose:** Display the analysis results, personalized based on onboarding answers if provided, and encourage account creation to save/unlock features.
    - **Appearance:** Follows JunkChk Design System for results display (`Card` component, `Grade` color/size, ingredient lists with icons). Includes action buttons at the bottom. Allergy/restriction highlights should be clearly visible if the user provided that information in the onboarding. [15]
    - **Key Elements:** Close icon (`X`, `Icon Button`) at top right to exit the scan flow, Product Name, Grade (colored), Summary, Ingredient Breakdown (Good/Bad/Allergy - clearly highlighting selected allergies/restrictions), Buttons: "Save This Result & Unlock Features" (`Primary Button`), "Scan Another" (`Secondary Button`). A small text label like "This scan is not saved." might appear subtly.
    - **Interactions & Navigation:**
      - Tap Close (X): Navigates back to the `Welcome Screen` or a simplified `Anonymous Home Screen` (if implemented, otherwise `Welcome`). The anonymous scan result and associated personalization data are typically discarded unless the user signs up.
      - Tap "Scan Another": Navigates back to the `Scan Screen`.
      - Tap "Save This Result & Unlock Features": Initiates the account creation flow -> Navigates to `Terms & Privacy Confirmation Screen`.

## User Flow: Account Creation (Post-Hook & Personalization)

This flow happens _after_ the user has successfully performed an anonymous scan and decided they want to save it and unlock features. The personalization data gathered during onboarding should be associated with the new account.

10. **Confirm Terms & Privacy**

    - **Screen:** Terms & Privacy Confirmation Screen
    - **Purpose:** Get explicit user agreement before creating an account.
    - **Appearance:** Similar structure to World App **S2**'s legal text area, but filling the main content space. Title ("Agree to Terms", `Typography.Heading1`), descriptive text about creating an account, the full legal text area with scrollability (or link to view), `Checkbox` component, Button: "Agree & Create Account" (`Primary Button`, disabled until checkbox is checked).
    - **Key Elements:** Title, Description, Legal Text/Links, Checkbox, "Agree & Create Account" Button. Back arrow (`<` or `←`, `Icon Button`) to return to `Scan Results Screen`.
    - **Interactions & Navigation:**
      - Tap Back arrow: Navigates back to the `Scan Results Screen (Anonymous)`.
      - Tap Legal Links: Opens documents.
      - Tap Checkbox: Enables/disables the button.
      - Tap "Agree & Create Account": Navigates to `Creating Account Screen`.

11. **Creating Account**

    - **Screen:** Creating Account Screen
    - **Purpose:** Indicate that the app is setting up the user's account and saving their personalization data.
    - **Appearance:** Mirrors World App **S3**. White background, centered spinner, Title ("Creating your account", `Typography.Heading1`), Description ("Saving your preferences and scan history...", `Typography.Description`).
    - **Key Elements:** Spinner, Title, Description.
    - **Interactions & Navigation:**
      - No user interaction. Automatically navigates to `Account Created Screen` upon completion.

12. **Account Created Confirmation**

    - **Screen:** Account Created Screen
    - **Purpose:** Confirm successful account creation and acknowledge personalization was saved.
    - **Appearance:** Mirrors World App **S1**. White background, green checkmark `Status Indicator`, Title ("Account created", `Typography.Heading1`), Description ("Your account has been successfully created, and your preferences saved.", `Typography.BodyText`). Button at bottom: "Continue".
    - **Key Elements:** Success Status Indicator, Title, Description, "Continue" Button.
    - **Interactions & Navigation:**
      - Tap "Continue": Navigates to the `History List Screen` (the main app home).

## User Flow: Main App Navigation & Features (Logged In)

Once an account is created, the user lands on the main app screen (likely History) and can access other features via navigation (e.g., a bottom tab bar). A bottom tab bar might include Scan, History, and Settings/Profile.

**Main Navigation Tabs:**

- Scan (Accesses the `Scan Screen`)
- History (Accesses the `History List Screen`)
- Settings (Accesses the `Settings Main Screen`)

### 13. History List Screen (Main App Home)

- **Purpose:** Display a list of all _saved_ (post-sign-up) scanned products. This is the default "home" screen after account creation.
- **Appearance:** Follows JunkChk Design System. Title ("History", `Typography.Heading1`). List of `List Row` components for each scan (Product Name, Date, small Grade indicator, right arrow). Empty state message if no history.
- **Key Elements:** Title, List of Scan History `List Row` items, Empty State Text.
- **Interactions & Navigation:**
  - Scroll list.
  - Tap a `List Row` item: Navigates to the `Scan Results Screen` for the saved scan.
  - Swipe on item (optional): Reveal Delete option.

### 14. Scan Screen (Via Main Nav)

- **Purpose:** Allows logged-in users to scan new products.
- **Appearance:** Same as Screen 7 in the onboarding flow, but includes a Back arrow (`<` or `←`, `Icon Button`) to return to the main app screen (e.g., History).
- **Interactions & Navigation:**
  - Tap Back arrow: Navigates back to the `History List Screen`.
  - Tap Capture Button: Takes photo, navigates to `Processing Screen`.

### 15. Scan Results Screen (Logged In)

- **Purpose:** Display results for a new scan or a historical scan. Personalization (allergies/goals) should influence the display here.
- **Appearance:** Same as Screen 9, but the bottom buttons change.
- **Key Elements:** Close icon (X) or Back arrow (<) depending on how it was accessed (Close if from new scan flow, Back if from History). Product Name, Grade, Summary, Ingredient Breakdown (clearly highlighting user-specified allergies/restrictions), Buttons: "Save Scan" (if new, not yet saved - `Primary Button`), "Scan Another" (`Secondary Button`), "Compare" (`Secondary Button`, maybe only visible if at least one other item is in history/comparison list). The subtle "This scan is not saved." message is only shown for _new_ scans before tapping "Save Scan".
- **Interactions & Navigation:**
  - Tap Close (X): Returns to `History List Screen` (if new scan, the result is _not_ saved unless "Save Scan" is tapped).
  - Tap Back (<): Returns to `History List Screen` (if from history) or previous screen (if from comparison flow).
  - Tap "Save Scan": Saves the scan to history, button changes state (e.g., "Saved"), potentially navigates back to history or stays on screen with updated state.
  - Tap "Scan Another": Navigates back to `Scan Screen`.
  - Tap "Compare": Navigates to `Comparison Selection Screen`.

### 16. Comparison Selection Screen

- **Purpose:** Select items from history for comparison.
- **Appearance:** Title, Instructions, List of History items with `Checkbox` component, "Compare Selected" Button. Back arrow icon.
- **Interactions & Navigation:**
  - Select/deselect items. Button enables/disables.
  - Tap "Compare Selected": Navigates to `Comparison View Screen`.
  - Tap Back arrow: Navigates back to `History List Screen` or `Scan Results Screen`.

### 17. Comparison View Screen

- **Purpose:** Display side-by-side/stacked comparison. Personalization (allergies/goals) should influence the display here.
- **Appearance:** Title, Comparison layout using `Card` components per product. Back arrow icon.
- **Key Elements:** Title, Comparison Cards (highlighting relevant info based on personalization).
- **Interactions & Navigation:**
  - Scroll to view comparison.
  - Tap Back arrow: Navigates back to `Comparison Selection Screen`.

### 18. Settings Main Screen

- **Purpose:** Access various app settings, including the ability to edit personalization answers.
- **Appearance:** Mirrors World App **S11**. Title ("Settings", `Typography.Heading2`), Close icon (X). List of `List Row` options: "Account", "Allergies & Restrictions", "Subscription", "Support", "About/Legal". Version number at bottom. Optional "Finish Setup" card if steps like phone linking are pending.
- **Key Elements:** Title, Close icon, List of `List Row` items, Version number.
- **Interactions & Navigation:**
  - Tap Close (X): Dismisses settings, returns to the main app screen (e.g., History).
  - Tap a `List Row`: Navigates to the corresponding settings sub-screen.

### 19. Settings - Account Screen

- **Purpose:** Manage account details, primarily focusing on data deletion and backup/recovery options.
- **Appearance:** Similar to World App **S12** top section. Title ("Account", `Typography.Heading1`). Section for account info (e.g., User ID if applicable). Section for Data Deletion (similar to S12). _Add a section for Account Recovery/Backup._
  - **Add Recovery/Backup:** A section titled "Account Recovery" or "Link Phone Number" (`Typography.Heading2`). Description explaining its purpose (restore backups). A `List Row` or button "Link Phone Number" or "Setup Recovery" which navigates to a phone number input screen (mimicking World App S4/S5 structure).
- **Key Elements:** Title, Account Info display, Recovery/Backup Section (Link Phone Number action), Data Deletion Section (`List Row` or Button for "Delete Account Data"). Back arrow (`<` or `←`, `Icon Button`).
- **Interactions & Navigation:**
  - Tap Back arrow: Navigates back to `Settings Main Screen`.
  - Tap "Link Phone Number" / "Setup Recovery": Navigates to a flow similar to World App S4 (Enter Phone Number) -> World App S5 (Enter SMS Code). Success confirms linking.
  - Tap "Delete Account Data": Triggers the `Confirm Deletion Modal`.

### 20. Settings - Allergies & Restrictions Screen

- **Purpose:** Manage the user's list of allergens and dietary restrictions, editing the information provided during onboarding. [15, 16, 17]
- **Appearance:** Modified from the onboarding Screen 4 concept. Title ("Allergies & Restrictions", `Typography.Heading1`), Instructions, Add Ingredient `Input Field` and "Add" Button, List of current allergens/restrictions with removal options. Back arrow icon.
- **Key Elements:** Title, Instructions, Input Field, Add Button, List of Allergens/Restrictions.
- **Interactions & Navigation:**
  - Add/Remove allergens/restrictions.
  - Tap Back arrow: Navigates back to `Settings Main Screen`.

### 21. Settings - Subscription Screen

- **Purpose:** View and manage subscription status.
- **Appearance:** Title ("Subscription", `Typography.Heading1`), Status display, Premium Benefits list, Action Buttons (Upgrade/Manage). Back arrow icon.
- **Key Elements:** Title, Status, Benefits, Action Buttons.
- **Interactions & Navigation:**
  - Manage subscription.
  - Tap Back arrow: Navigates back to `Settings Main Screen`.

### 22. Settings - Support Screen

- **Purpose:** Access help resources.
- **Appearance:** Title ("Support", `Typography.Heading1`), List of `List Row` options (FAQ, Contact Us, Report). Back arrow icon.
- **Key Elements:** Title, Support Options List.
- **Interactions & Navigation:**
  - Access support resources.
  - Tap Back arrow: Navigates back to `Settings Main Screen`.

### 23. Settings - About/Legal Screen

- **Purpose:** View app information and legal notices.
- **Appearance:** Title ("About", `Typography.Heading1`), App Info, Legal Links (`List Row` or Text Links). Back arrow icon.
- **Key Elements:** Title, App Info, Legal Links.
- **Interactions & Navigation:**
  - View legal documents.
  - Tap Back arrow: Navigates back to `Settings Main Screen`.

### 24. Confirm Deletion Modal

- **Purpose:** Confirm irreversible data deletion.
- **Appearance:** Mirrors World App **S12** modal. Overlay, Modal Content `Card` with rounded top corners. Red trash can `Status Indicator`. Title ("Confirm Deletion", `Typography.Heading2`), Warning Description (`Typography.BodyText`). Buttons: "Hold to Delete" (`Primary Button`, emphasizes danger/action), "Cancel" (`Secondary Button`). Close icon (X).
- **Key Elements:** Overlay, Modal Card, Error Status Indicator, Title, Description, "Hold to Delete" Button, "Cancel" Button, Close icon.
- **Interactions & Navigation:**
  - Tap "Cancel" or Close (X): Dismisses modal, returns to `Settings - Account Screen`.
  - Tap/Hold "Hold to Delete": Initiates deletion process. Upon success, navigates back to the `Welcome Screen`.

## Considerations for Developers

- **State Management:** Clearly define the app's state (logged in/out, current scan data, history data, allergy/restriction list, user goals, subscription status). The personalization data gathered during onboarding needs to be stored temporarily for anonymous users and permanently associated with an account upon creation.
- **Permissions:** Camera permission must be requested before accessing the `Scan Screen`.
- **Network:** Most analysis requires an internet connection. Implement clear error handling and feedback (`Error` Status Indicator, `Modal`) if the network is unavailable during processing or API calls fail (mimicking World App S6 concept - "Something went wrong").
- **Keyboard Management:** Ensure input fields (like adding custom restrictions or phone number) handle the keyboard gracefully on mobile.
- **Navigation Implementation:** Choose a robust React Native navigation library (e.g., React Navigation) and implement the transitions as described. Ensure the back button behavior is correct for each screen, especially within the multi-step onboarding. Use a progress indicator UI component for the onboarding questions. [4, 9]
- **Animations:** Subtle animations for screen transitions, modal appearance/dismissal, loading states (spinners), and potentially the progress bar animation will enhance the polished feel, aligning with the World App aesthetic.
- **Freemium Logic:** Implement logic to show ads (where desired, e.g., on anonymous results, history list for free users) and gate premium features (comparison limit for free users? unlimited allergies/restrictions for premium? ad-free experience). The personalization data could potentially influence freemium limitations or premium feature highlighting.
- **Personalization Implementation:** Use the stored user goals and allergy/restriction data to dynamically adjust the content and highlighting within the `Scan Results Screen` and `Comparison View Screen`. This is where the value of the onboarding questions is delivered.
- **Onboarding Question Design:** Keep the questions concise and easy to answer using multiple-choice or tappable options. [7, 9] Allow users to skip this section if desired. [3]

This detailed flow, combined with the Design System, should provide a comprehensive guide for building JunkChk with the desired user experience and visual style, incorporating an engaging and personalized onboarding sequence.