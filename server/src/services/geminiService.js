const { GoogleGenerativeAI } = require('@google/generative-ai');
const logger = require('../utils/logger');

class GeminiService {
  constructor() {
    this.apiKey = process.env.GOOGLE_API_KEY;
    this.modelName = process.env.GEMINI_MODEL || 'gemini-2.5-flash-preview-05-20';
    this.timeout = parseInt(process.env.REQUEST_TIMEOUT) || 50000; // 50 seconds

    if (!this.apiKey) {
      throw new Error('GOOGLE_API_KEY environment variable is required');
    }

    this.genAI = new GoogleGenerativeAI(this.apiKey);
    this.model = this.genAI.getGenerativeModel({
      model: this.modelName,
    });

    // Default generation config
    this.generationConfig = {
      temperature: 0.2,
      topP: 0.8,
      topK: 40,
    };

    logger.info('GeminiService initialized', {
      model: this.modelName,
      timeout: this.timeout
    });
  }

  /**
   * Analyze single image with Gemini API
   * @param {string} base64Image - Base64 encoded image
   * @param {string} prompt - Analysis prompt
   * @returns {Promise<Object>} - Analysis result
   */
  async analyzeSingleImage(base64Image, prompt) {
    try {
      logger.info('Starting single image analysis');

      const imagePart = {
        inlineData: {
          data: base64Image,
          mimeType: "image/jpeg",
        },
      };

      const contents = {
        contents: [
          {
            parts: [{ text: prompt }, imagePart],
          },
        ],
        generationConfig: this.generationConfig
      };

      const result = await this.makeApiCall(contents);
      return this.parseResponse(result);
    } catch (error) {
      logger.error('Error in single image analysis:', error);
      throw this.handleApiError(error);
    }
  }

  /**
   * Analyze multiple images with Gemini API
   * @param {Array<string>} base64Images - Array of base64 encoded images
   * @param {string} prompt - Analysis prompt
   * @returns {Promise<Object>} - Analysis result
   */
  async analyzeMultipleImages(base64Images, prompt) {
    try {
      logger.info('Starting multiple image analysis', { imageCount: base64Images.length });

      const imageParts = base64Images.map(base64Image => ({
        inlineData: {
          data: base64Image,
          mimeType: "image/jpeg",
        },
      }));

      const contents = {
        contents: [
          {
            parts: [{ text: prompt }, ...imageParts],
          },
        ],
        generationConfig: this.generationConfig
      };

      const result = await this.makeApiCall(contents);
      return this.parseResponse(result);
    } catch (error) {
      logger.error('Error in multiple image analysis:', error);
      throw this.handleApiError(error);
    }
  }

  /**
   * Multi-stage analysis with enhanced context awareness
   * @param {Array<string>} base64Images - Array of base64 encoded images
   * @returns {Promise<Object>} - Complete analysis result
   */
  async analyzeMultiStage(base64Images) {
    try {
      logger.info('Starting multi-stage analysis', { imageCount: base64Images.length });

      const promptService = require('./promptService');

      // Prepare image parts for all stages
      const imageParts = base64Images.map(base64Image => ({
        inlineData: {
          data: base64Image,
          mimeType: "image/jpeg",
        },
      }));

      // Stage 1: Product Type Classification
      logger.info('Stage 1: Product type classification');
      const stage1Prompt = promptService.getStage1Prompt();
      const stage1Result = await this.executeStage(imageParts, stage1Prompt, 'Stage 1');

      if (stage1Result.error) {
        return stage1Result;
      }

      // Stage 2: Product Identification
      logger.info('Stage 2: Product identification');
      const stage2Prompt = promptService.getStage2Prompt(stage1Result);
      const stage2Result = await this.executeStage(imageParts, stage2Prompt, 'Stage 2');

      if (stage2Result.error) {
        return stage2Result;
      }

      // Stage 3: Ingredient Extraction (only for cosmetic/food products)
      let stage3Result = null;
      if (stage1Result.product_type === 'cosmetic' || stage1Result.product_type === 'food') {
        logger.info('Stage 3: Ingredient extraction');
        const stage3Prompt = promptService.getStage3Prompt(stage1Result, stage2Result);
        if (stage3Prompt) {
          stage3Result = await this.executeStage(imageParts, stage3Prompt, 'Stage 3');
          if (stage3Result.error) {
            return stage3Result;
          }
        }
      }

      // Stage 4: Expert Analysis & Rating (only for cosmetic/food products)
      let stage4Result = null;
      if (stage1Result.product_type === 'cosmetic' || stage1Result.product_type === 'food') {
        logger.info('Stage 4: Expert analysis and rating');
        const stage4Prompt = promptService.getStage4Prompt(stage1Result, stage2Result, stage3Result);
        if (stage4Prompt) {
          stage4Result = await this.executeStage(imageParts, stage4Prompt, 'Stage 4');
          if (stage4Result.error) {
            return stage4Result;
          }
        }
      }

      // Combine results into final response
      const finalResult = this.combineStageResults(stage1Result, stage2Result, stage3Result, stage4Result);

      logger.info('Multi-stage analysis completed successfully');
      return finalResult;

    } catch (error) {
      logger.error('Error in multi-stage analysis:', error);
      throw this.handleApiError(error);
    }
  }

  /**
   * Execute a single stage of analysis
   * @param {Array} imageParts - Image parts for API call
   * @param {string} prompt - Stage-specific prompt
   * @param {string} stageName - Name of the stage for logging
   * @returns {Promise<Object>} - Stage result
   */
  async executeStage(imageParts, prompt, stageName) {
    try {
      const contents = {
        contents: [
          {
            parts: [{ text: prompt }, ...imageParts],
          },
        ],
        generationConfig: this.generationConfig
      };

      const result = await this.makeApiCall(contents);
      const parsedResult = this.parseResponse(result);

      logger.info(`${stageName} completed successfully`);
      return parsedResult;
    } catch (error) {
      logger.error(`Error in ${stageName}:`, error);
      throw error;
    }
  }

  /**
   * Combine results from all stages into final response
   * @param {Object} stage1Result - Product type classification
   * @param {Object} stage2Result - Product identification
   * @param {Object} stage3Result - Ingredient extraction (optional)
   * @param {Object} stage4Result - Expert analysis (optional)
   * @returns {Object} - Combined final result
   */
  combineStageResults(stage1Result, stage2Result, stage3Result, stage4Result) {
    // For non-cosmetic/food products, return basic details only
    if (stage1Result.product_type !== 'cosmetic' && stage1Result.product_type !== 'food') {
      return {
        product_details: {
          name: stage2Result.product_details?.name || 'Unknown Product',
          ingredients: stage2Result.product_details?.ingredients || []
        },
        summary: `This ${stage1Result.product_type} product has been identified but does not require ingredient analysis.`,
        explanation: {
          influencing_ingredients: [],
          rationale: `Product classified as ${stage1Result.product_type} - no safety rating applicable.`
        }
      };
    }

    // For cosmetic/food products, combine all stage results
    const combinedResult = {
      // Include confidence warning if present in stage 4
      ...(stage4Result?.confidence_warning && { confidence_warning: stage4Result.confidence_warning }),

      // Core product information
      rating: stage4Result?.rating || 'C',
      product_details: {
        name: stage2Result.product_details?.name || 'Unknown Product',
        ingredients: stage3Result?.ingredients?.map(ing => ing.name) || stage2Result.product_details?.ingredients || []
      },

      // User-friendly summary and effects
      summary: stage4Result?.summary || 'Analysis completed with limited information.',
      effects: stage4Result?.effects || {
        positive_effects: { short_term: [], long_term: [] },
        negative_effects: { short_term: [], long_term: [] }
      },

      // Ingredient analysis
      good_ingredients: stage4Result?.good_ingredients || [],
      harmful_ingredient_analysis: stage4Result?.harmful_ingredient_analysis || [],

      // Technical explanation
      explanation: {
        influencing_ingredients: stage4Result?.explanation?.influencing_ingredients || [],
        rationale: stage4Result?.explanation?.rationale || 'Analysis completed using multi-stage approach.'
      }
    };

    return combinedResult;
  }

  /**
   * Make API call with timeout handling
   * @param {Object} contents - Request contents
   * @returns {Promise<Object>} - API response
   */
  async makeApiCall(contents) {
    try {
      logger.info('Making Gemini API call');

      // Create timeout promise
      const timeoutPromise = new Promise((_, reject) =>
        setTimeout(() => reject(new Error('API request timed out')), this.timeout)
      );

      // Make API call
      const apiPromise = this.model.generateContent(contents);

      // Race between API call and timeout
      const result = await Promise.race([apiPromise, timeoutPromise]);

      logger.info('Gemini API call successful');
      logger.info('Response structure:', {
        hasResponse: !!result.response,
        responseType: typeof result.response,
        responseKeys: result.response ? Object.keys(result.response) : [],
        hasText: result.response && typeof result.response.text !== 'undefined'
      });

      return result;
    } catch (error) {
      logger.error('Gemini API call failed:', error);
      throw error;
    }
  }

  /**
   * Clean response text to remove markdown formatting
   * @param {string} responseText - Raw response text
   * @returns {string} - Cleaned response text
   */
  cleanResponseText(responseText) {
    try {
      // Remove markdown code blocks (```json ... ``` or ``` ... ```)
      let cleaned = responseText.replace(/```json\s*/g, '').replace(/```\s*/g, '');

      // Remove any leading/trailing whitespace
      cleaned = cleaned.trim();

      // If the response starts and ends with backticks, remove them
      if (cleaned.startsWith('`') && cleaned.endsWith('`')) {
        cleaned = cleaned.slice(1, -1).trim();
      }

      // Remove any other markdown formatting that might interfere
      cleaned = cleaned.replace(/^\s*```.*$/gm, ''); // Remove any remaining ``` lines

      return cleaned;
    } catch (error) {
      logger.error('Error cleaning response text:', error);
      return responseText; // Return original if cleaning fails
    }
  }

  /**
   * Parse and validate API response
   * @param {Object} result - Raw API response
   * @returns {Object} - Parsed response
   */
  parseResponse(result) {
    try {
      if (!result.response) {
        logger.error('No response object from Gemini API');
        throw new Error('No response from Gemini API');
      }

      // Get response text - handle both sync and async cases
      let responseText;
      try {
        responseText = typeof result.response.text === 'function'
          ? result.response.text()
          : result.response.text;
      } catch (textError) {
        logger.error('Error extracting text from response:', textError);
        throw new Error('Failed to extract text from API response');
      }

      logger.info('Response text extraction successful, length:', responseText ? responseText.length : 0);

      if (!responseText || responseText.trim() === '') {
        logger.error('Empty or null response text from Gemini API');
        logger.error('Full response object:', JSON.stringify(result.response, null, 2));
        throw new Error('Empty response from Gemini API');
      }

      logger.info('Parsing Gemini response');
      logger.info('Raw response text (first 500 chars):', responseText.substring(0, 500));

      // Clean response text to remove markdown formatting
      const cleanedResponseText = this.cleanResponseText(responseText);
      logger.info('Cleaned response text (first 500 chars):', cleanedResponseText.substring(0, 500));

      // Try to parse as JSON
      let parsedResponse;
      try {
        parsedResponse = JSON.parse(cleanedResponseText);
        logger.info('Successfully parsed JSON response');
        logger.info('Parsed response structure:', {
          hasError: !!parsedResponse.error,
          hasRating: !!parsedResponse.rating,
          hasProductDetails: !!parsedResponse.product_details,
          topLevelKeys: Object.keys(parsedResponse)
        });
      } catch (parseError) {
        logger.error('Failed to parse response as JSON:', parseError);
        logger.error('Cleaned response text that failed to parse:', cleanedResponseText);
        // Return a technical error response
        return {
          error: {
            code: 'TECHNICAL_ERROR',
            message: 'Failed to parse API response. Please try again later.'
          }
        };
      }

      // Validate response structure
      if (parsedResponse.error) {
        logger.info('API returned error response:', parsedResponse.error);
        return parsedResponse;
      }

      // More flexible validation - accept any valid JSON response
      if (typeof parsedResponse === 'object' && parsedResponse !== null) {
        logger.info('Successfully parsed analysis response');
        return parsedResponse;
      }

      // If response doesn't match expected format, return technical error
      logger.warn('Response format validation failed - not a valid object');
      return {
        error: {
          code: 'TECHNICAL_ERROR',
          message: 'Unexpected response format. Please try again later.'
        }
      };
    } catch (error) {
      logger.error('Error parsing response:', error);
      return {
        error: {
          code: 'TECHNICAL_ERROR',
          message: 'Failed to process API response. Please try again later.'
        }
      };
    }
  }

  /**
   * Handle API errors and convert to standard format
   * @param {Error} error - Original error
   * @returns {Error} - Formatted error
   */
  handleApiError(error) {
    logger.error('Handling API error:', error);

    if (error.message.includes('timeout') || error.message.includes('timed out')) {
      const timeoutError = new Error('Request timed out');
      timeoutError.code = 'TIMEOUT_ERROR';
      timeoutError.statusCode = 408;
      return timeoutError;
    }

    if (error.message.includes('API key') || error.message.includes('authentication')) {
      const authError = new Error('Authentication failed');
      authError.code = 'AUTH_ERROR';
      authError.statusCode = 401;
      return authError;
    }

    if (error.message.includes('quota') || error.message.includes('limit')) {
      const quotaError = new Error('API quota exceeded');
      quotaError.code = 'QUOTA_ERROR';
      quotaError.statusCode = 429;
      return quotaError;
    }

    // Default to technical error
    const technicalError = new Error('Technical error occurred');
    technicalError.code = 'TECHNICAL_ERROR';
    technicalError.statusCode = 500;
    return technicalError;
  }
}

module.exports = new GeminiService();
