const sharp = require('sharp');
const logger = require('./logger');

class ImageProcessor {
  constructor() {
    this.maxWidth = 1024;
    this.quality = 90;
    this.format = 'jpeg';
  }

  /**
   * Resize and optimize image
   * @param {Buffer} imageBuffer - Input image buffer
   * @param {Object} options - Processing options
   * @returns {Promise<Buffer>} - Processed image buffer
   */
  async processImage(imageBuffer, options = {}) {
    try {
      const {
        maxWidth = this.maxWidth,
        quality = this.quality,
        format = this.format
      } = options;

      logger.info('Processing image', {
        originalSize: imageBuffer.length,
        maxWidth,
        quality,
        format
      });

      const processedBuffer = await sharp(imageBuffer)
        .resize(maxWidth, null, {
          withoutEnlargement: true,
          fit: 'inside'
        })
        .jpeg({ quality })
        .toBuffer();

      logger.info('Image processed successfully', {
        originalSize: imageBuffer.length,
        processedSize: processedBuffer.length,
        compressionRatio: ((imageBuffer.length - processedBuffer.length) / imageBuffer.length * 100).toFixed(2) + '%'
      });

      return processedBuffer;
    } catch (error) {
      logger.error('Error processing image:', error);
      throw new Error('Failed to process image');
    }
  }

  /**
   * Convert image buffer to base64
   * @param {Buffer} imageBuffer - Image buffer
   * @returns {string} - Base64 encoded string
   */
  bufferToBase64(imageBuffer) {
    try {
      return imageBuffer.toString('base64');
    } catch (error) {
      logger.error('Error converting buffer to base64:', error);
      throw new Error('Failed to convert image to base64');
    }
  }

  /**
   * Validate image format and size
   * @param {Buffer} imageBuffer - Image buffer to validate
   * @param {number} maxSize - Maximum file size in bytes
   * @returns {Promise<Object>} - Validation result
   */
  async validateImage(imageBuffer, maxSize = 10 * 1024 * 1024) { // 10MB default
    try {
      // Check file size
      if (imageBuffer.length > maxSize) {
        return {
          valid: false,
          error: `File size exceeds maximum limit of ${maxSize / (1024 * 1024)}MB`
        };
      }

      // Get image metadata
      const metadata = await sharp(imageBuffer).metadata();
      
      // Check if it's a valid image
      if (!metadata.format) {
        return {
          valid: false,
          error: 'Invalid image format'
        };
      }

      // Check supported formats
      const supportedFormats = ['jpeg', 'jpg', 'png', 'webp'];
      if (!supportedFormats.includes(metadata.format.toLowerCase())) {
        return {
          valid: false,
          error: `Unsupported image format: ${metadata.format}. Supported formats: ${supportedFormats.join(', ')}`
        };
      }

      return {
        valid: true,
        metadata: {
          format: metadata.format,
          width: metadata.width,
          height: metadata.height,
          size: imageBuffer.length
        }
      };
    } catch (error) {
      logger.error('Error validating image:', error);
      return {
        valid: false,
        error: 'Failed to validate image'
      };
    }
  }

  /**
   * Process multiple images
   * @param {Array<Buffer>} imageBuffers - Array of image buffers
   * @param {Object} options - Processing options
   * @returns {Promise<Array<string>>} - Array of base64 encoded images
   */
  async processMultipleImages(imageBuffers, options = {}) {
    try {
      const processedImages = await Promise.all(
        imageBuffers.map(async (buffer) => {
          const processedBuffer = await this.processImage(buffer, options);
          return this.bufferToBase64(processedBuffer);
        })
      );

      logger.info(`Successfully processed ${processedImages.length} images`);
      return processedImages;
    } catch (error) {
      logger.error('Error processing multiple images:', error);
      throw new Error('Failed to process images');
    }
  }
}

module.exports = new ImageProcessor();
