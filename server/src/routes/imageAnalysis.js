const express = require('express');
const multer = require('multer');
const imageController = require('../controllers/imageController');
const { validateFileUpload, validateBase64Images } = require('../middleware/validation');
const { asyncHandler } = require('../middleware/errorHandler');

const router = express.Router();

// Configure multer for file uploads
const upload = multer({
  storage: multer.memoryStorage(),
  limits: {
    fileSize: parseInt(process.env.MAX_FILE_SIZE) || 10 * 1024 * 1024, // 10MB
    files: parseInt(process.env.MAX_FILES) || 5
  },
  fileFilter: (req, file, cb) => {
    // Allow only image files
    const allowedMimeTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/webp'];
    if (allowedMimeTypes.includes(file.mimetype)) {
      cb(null, true);
    } else {
      cb(new Error(`Invalid file type: ${file.mimetype}. Allowed types: ${allowedMimeTypes.join(', ')}`), false);
    }
  }
});

/**
 * @route POST /api/analyze-image
 * @desc Analyze single image (file upload)
 * @access Public
 */
router.post('/analyze-image',
  upload.single('image'),
  validateFileUpload,
  asyncHandler(imageController.analyzeSingleImage)
);

/**
 * @route POST /api/analyze-image-base64
 * @desc Analyze single image (base64 in request body)
 * @access Public
 */
router.post('/analyze-image-base64',
  validateBase64Images,
  asyncHandler(imageController.analyzeSingleImage)
);

/**
 * @route POST /api/analyze-images
 * @desc Analyze multiple images (file upload)
 * @access Public
 */
router.post('/analyze-images',
  upload.array('images', parseInt(process.env.MAX_FILES) || 5),
  validateFileUpload,
  asyncHandler(imageController.analyzeMultipleImages)
);

/**
 * @route POST /api/analyze-images-base64
 * @desc Analyze multiple images (base64 in request body)
 * @access Public
 */
router.post('/analyze-images-base64',
  validateBase64Images,
  asyncHandler(imageController.analyzeMultipleImages)
);

/**
 * @route POST /api/analyze-multi-stage
 * @desc Multi-stage analysis for enhanced accuracy (file upload)
 * @access Public
 */
router.post('/analyze-multi-stage',
  upload.array('images', parseInt(process.env.MAX_FILES) || 5),
  validateFileUpload,
  asyncHandler(imageController.analyzeMultiStage)
);

/**
 * @route POST /api/analyze-multi-stage-base64
 * @desc Multi-stage analysis for enhanced accuracy (base64 in request body)
 * @access Public
 */
router.post('/analyze-multi-stage-base64',
  validateBase64Images,
  asyncHandler(imageController.analyzeMultiStage)
);

/**
 * @route GET /api/analysis-status
 * @desc Get analysis service status
 * @access Public
 */
router.get('/analysis-status',
  asyncHandler(imageController.getAnalysisStatus)
);

/**
 * @route GET /api/test
 * @desc Test analysis functionality
 * @access Public
 */
router.get('/test',
  asyncHandler(imageController.testAnalysis)
);

module.exports = router;
