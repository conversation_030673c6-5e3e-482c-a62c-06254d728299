const { body, validationResult } = require('express-validator');
const logger = require('../utils/logger');

/**
 * Validation rules for image analysis endpoints
 */
const imageAnalysisValidation = [
  body('images')
    .optional()
    .isArray({ min: 1, max: 5 })
    .withMessage('Images must be an array with 1-5 items'),

  body('images.*')
    .optional()
    .isBase64()
    .withMessage('Each image must be a valid base64 string'),

  body('analysisType')
    .optional()
    .isIn(['food', 'beauty', 'general'])
    .withMessage('Analysis type must be one of: food, beauty, general'),
];

/**
 * Middleware to handle validation errors
 */
const handleValidationErrors = (req, res, next) => {
  const errors = validationResult(req);

  if (!errors.isEmpty()) {
    logger.warn('Validation errors:', { errors: errors.array(), ip: req.ip });

    return res.status(400).json({
      error: {
        code: 'VALIDATION_ERROR',
        message: 'Invalid request data',
        details: errors.array()
      }
    });
  }

  next();
};

/**
 * Validate file upload middleware
 */
const validateFileUpload = (req, res, next) => {
  try {
    // Check if files were uploaded
    if (!req.files || req.files.length === 0) {
      return res.status(400).json({
        error: {
          code: 'NO_FILES',
          message: 'No files were uploaded'
        }
      });
    }

    // Check file count
    if (req.files.length > 5) {
      return res.status(400).json({
        error: {
          code: 'TOO_MANY_FILES',
          message: 'Maximum 5 files allowed'
        }
      });
    }

    // Check file sizes and types
    const maxSize = parseInt(process.env.MAX_FILE_SIZE) || 10 * 1024 * 1024; // 10MB
    const allowedMimeTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/webp'];

    for (const file of req.files) {
      // Check file size
      if (file.size > maxSize) {
        return res.status(400).json({
          error: {
            code: 'FILE_TOO_LARGE',
            message: `File ${file.originalname} exceeds maximum size of ${maxSize / (1024 * 1024)}MB`
          }
        });
      }

      // Check file type
      if (!allowedMimeTypes.includes(file.mimetype)) {
        return res.status(400).json({
          error: {
            code: 'INVALID_FILE_TYPE',
            message: `File ${file.originalname} has invalid type. Allowed types: ${allowedMimeTypes.join(', ')}`
          }
        });
      }
    }

    logger.info('File validation passed', {
      fileCount: req.files.length,
      totalSize: req.files.reduce((sum, file) => sum + file.size, 0)
    });

    next();
  } catch (error) {
    logger.error('File validation error:', error);
    res.status(500).json({
      error: {
        code: 'VALIDATION_ERROR',
        message: 'Error validating uploaded files'
      }
    });
  }
};

/**
 * Validate base64 images in request body
 */
const validateBase64Images = (req, res, next) => {
  try {
    logger.info('Validating base64 images', {
      bodyKeys: Object.keys(req.body),
      hasImages: !!req.body.images,
      bodyType: typeof req.body,
      bodyContent: req.body
    });

    const { images } = req.body;

    if (!images || !Array.isArray(images) || images.length === 0) {
      logger.warn('No images found in request body', {
        images: images,
        bodyKeys: Object.keys(req.body)
      });
      return res.status(400).json({
        error: {
          code: 'NO_IMAGES',
          message: 'No images provided in request body'
        }
      });
    }

    if (images.length > 5) {
      return res.status(400).json({
        error: {
          code: 'TOO_MANY_IMAGES',
          message: 'Maximum 5 images allowed'
        }
      });
    }

    // Validate each base64 string
    for (let i = 0; i < images.length; i++) {
      const image = images[i];

      if (typeof image !== 'string') {
        return res.status(400).json({
          error: {
            code: 'INVALID_IMAGE_FORMAT',
            message: `Image at index ${i} must be a string`
          }
        });
      }

      // Check if it's a valid base64 string
      const base64Regex = /^[A-Za-z0-9+/]*={0,2}$/;
      if (!base64Regex.test(image)) {
        return res.status(400).json({
          error: {
            code: 'INVALID_BASE64',
            message: `Image at index ${i} is not a valid base64 string`
          }
        });
      }

      // Check base64 size (approximate file size)
      const sizeInBytes = (image.length * 3) / 4;
      const maxSize = parseInt(process.env.MAX_FILE_SIZE) || 10 * 1024 * 1024; // 10MB

      if (sizeInBytes > maxSize) {
        return res.status(400).json({
          error: {
            code: 'IMAGE_TOO_LARGE',
            message: `Image at index ${i} exceeds maximum size of ${maxSize / (1024 * 1024)}MB`
          }
        });
      }
    }

    logger.info('Base64 image validation passed', {
      imageCount: images.length
    });

    next();
  } catch (error) {
    logger.error('Base64 image validation error:', error);
    res.status(500).json({
      error: {
        code: 'VALIDATION_ERROR',
        message: 'Error validating base64 images'
      }
    });
  }
};

module.exports = {
  imageAnalysisValidation,
  handleValidationErrors,
  validateFileUpload,
  validateBase64Images
};
