const logger = require('../utils/logger');

/**
 * 404 Not Found handler
 */
const notFound = (req, res, next) => {
  const error = new Error(`Not Found - ${req.originalUrl}`);
  error.statusCode = 404;
  next(error);
};

/**
 * Global error handler
 */
const errorHandler = (err, req, res, next) => {
  // Log the error
  logger.error('Error occurred:', {
    message: err.message,
    stack: err.stack,
    url: req.originalUrl,
    method: req.method,
    ip: req.ip,
    userAgent: req.get('User-Agent')
  });

  // Default error response
  let statusCode = err.statusCode || 500;
  let errorResponse = {
    error: {
      code: err.code || 'INTERNAL_SERVER_ERROR',
      message: err.message || 'An unexpected error occurred'
    }
  };

  // Handle specific error types
  switch (err.code) {
    case 'TIMEOUT_ERROR':
      statusCode = 408;
      errorResponse.error = {
        code: 'TIMEOUT_ERROR',
        message: 'Request timed out. Please try again later.'
      };
      break;

    case 'AUTH_ERROR':
      statusCode = 401;
      errorResponse.error = {
        code: 'AUTH_ERROR',
        message: 'Authentication failed. Please check your API configuration.'
      };
      break;

    case 'QUOTA_ERROR':
      statusCode = 429;
      errorResponse.error = {
        code: 'QUOTA_ERROR',
        message: 'API quota exceeded. Please try again later.'
      };
      break;

    case 'VALIDATION_ERROR':
      statusCode = 400;
      // Keep the original validation error message
      break;

    case 'FILE_TOO_LARGE':
    case 'TOO_MANY_FILES':
    case 'INVALID_FILE_TYPE':
    case 'NO_FILES':
    case 'NO_IMAGES':
    case 'TOO_MANY_IMAGES':
    case 'INVALID_IMAGE_FORMAT':
    case 'INVALID_BASE64':
    case 'IMAGE_TOO_LARGE':
      statusCode = 400;
      // Keep the original error message
      break;

    case 'RATE_LIMIT_EXCEEDED':
      statusCode = 429;
      errorResponse.error = {
        code: 'RATE_LIMIT_EXCEEDED',
        message: 'Too many requests. Please try again later.'
      };
      break;

    default:
      // Handle common HTTP errors
      if (err.name === 'ValidationError') {
        statusCode = 400;
        errorResponse.error = {
          code: 'VALIDATION_ERROR',
          message: 'Invalid request data'
        };
      } else if (err.name === 'CastError') {
        statusCode = 400;
        errorResponse.error = {
          code: 'INVALID_ID',
          message: 'Invalid ID format'
        };
      } else if (err.name === 'MulterError') {
        statusCode = 400;
        if (err.code === 'LIMIT_FILE_SIZE') {
          errorResponse.error = {
            code: 'FILE_TOO_LARGE',
            message: 'File size exceeds the maximum limit'
          };
        } else if (err.code === 'LIMIT_FILE_COUNT') {
          errorResponse.error = {
            code: 'TOO_MANY_FILES',
            message: 'Too many files uploaded'
          };
        } else {
          errorResponse.error = {
            code: 'UPLOAD_ERROR',
            message: 'File upload error'
          };
        }
      }
      break;
  }

  // Don't expose internal errors in production
  if (process.env.NODE_ENV === 'production' && statusCode === 500) {
    errorResponse.error = {
      code: 'TECHNICAL_ERROR',
      message: 'An error occurred while processing your request. Please try again later.'
    };
  }

  // Add request ID for tracking (if available)
  if (req.id) {
    errorResponse.requestId = req.id;
  }

  // Add timestamp
  errorResponse.timestamp = new Date().toISOString();

  res.status(statusCode).json(errorResponse);
};

/**
 * Async error wrapper
 */
const asyncHandler = (fn) => (req, res, next) => {
  Promise.resolve(fn(req, res, next)).catch(next);
};

module.exports = {
  notFound,
  errorHandler,
  asyncHandler
};
