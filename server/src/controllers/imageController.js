const geminiService = require('../services/geminiService');
const promptService = require('../services/promptService');
const imageProcessor = require('../utils/imageProcessor');
const logger = require('../utils/logger');

class ImageController {
  /**
   * Analyze single image
   */
  async analyzeSingleImage(req, res, next) {
    try {
      logger.info('Starting single image analysis');

      let base64Image;

      // Handle file upload
      if (req.files && req.files.length > 0) {
        const imageBuffer = req.files[0].buffer;

        // Validate image
        const validation = await imageProcessor.validateImage(imageBuffer);
        if (!validation.valid) {
          return res.status(400).json({
            error: {
              code: 'INVALID_IMAGE',
              message: validation.error
            }
          });
        }

        // Process image
        const processedBuffer = await imageProcessor.processImage(imageBuffer);
        base64Image = imageProcessor.bufferToBase64(processedBuffer);
      }
      // Handle base64 image in request body
      else if (req.body.image) {
        base64Image = req.body.image;
      }
      // Handle base64 images array (for compatibility with validation middleware)
      else if (req.body.images && Array.isArray(req.body.images) && req.body.images.length > 0) {
        base64Image = req.body.images[0]; // Take the first image
      }
      else {
        return res.status(400).json({
          error: {
            code: 'NO_IMAGE',
            message: 'No image provided'
          }
        });
      }

      // Extract user allergies if provided
      const allergies = Array.isArray(req.body.allergies) ? req.body.allergies : [];

      // Get analysis prompt with user context
      const prompt = promptService.getAnalysisPrompt(allergies);

      // Analyze image with Gemini
      const result = await geminiService.analyzeSingleImage(base64Image, prompt);

      logger.info('Single image analysis completed successfully');

      res.json({
        success: true,
        data: result,
        timestamp: new Date().toISOString()
      });

    } catch (error) {
      logger.error('Error in single image analysis:', error);
      next(error);
    }
  }

  /**
   * Analyze multiple images
   */
  async analyzeMultipleImages(req, res, next) {
    try {
      logger.info('Starting multiple image analysis');

      let base64Images = [];

      // Handle file uploads
      if (req.files && req.files.length > 0) {
        // Validate all images first
        for (const file of req.files) {
          const validation = await imageProcessor.validateImage(file.buffer);
          if (!validation.valid) {
            return res.status(400).json({
              error: {
                code: 'INVALID_IMAGE',
                message: `${file.originalname}: ${validation.error}`
              }
            });
          }
        }

        // Process all images
        const imageBuffers = req.files.map(file => file.buffer);
        base64Images = await imageProcessor.processMultipleImages(imageBuffers);
      }
      // Handle base64 images in request body
      else if (req.body.images && Array.isArray(req.body.images)) {
        base64Images = req.body.images;
      }
      else {
        return res.status(400).json({
          error: {
            code: 'NO_IMAGES',
            message: 'No images provided'
          }
        });
      }

      if (base64Images.length === 0) {
        return res.status(400).json({
          error: {
            code: 'NO_IMAGES',
            message: 'No valid images found'
          }
        });
      }

      // Extract user allergies if provided
      const allergies = Array.isArray(req.body.allergies) ? req.body.allergies : [];

      // Get analysis prompt with user context
      const prompt = promptService.getAnalysisPrompt(allergies);

      // Analyze images with Gemini
      const result = await geminiService.analyzeMultipleImages(base64Images, prompt);

      logger.info('Multiple image analysis completed successfully', {
        imageCount: base64Images.length
      });

      res.json({
        success: true,
        data: result,
        imageCount: base64Images.length,
        timestamp: new Date().toISOString()
      });

    } catch (error) {
      logger.error('Error in multiple image analysis:', error);
      next(error);
    }
  }

  /**
   * Multi-stage analysis - Single API call with comprehensive prompt
   */
  async analyzeMultiStage(req, res, next) {
    try {
      logger.info('Starting single-call multi-stage analysis');

      let base64Image;

      // Handle file uploads
      if (req.files && req.files.length > 0) {
        const imageBuffer = req.files[0].buffer;

        // Validate image
        const validation = await imageProcessor.validateImage(imageBuffer);
        if (!validation.valid) {
          return res.status(400).json({
            error: {
              code: 'INVALID_IMAGE',
              message: validation.error
            }
          });
        }

        // Process image
        const processedBuffer = await imageProcessor.processImage(imageBuffer);
        base64Image = imageProcessor.bufferToBase64(processedBuffer);
      }
      // Handle base64 images in request body
      else if (req.body.images && Array.isArray(req.body.images) && req.body.images.length > 0) {
        base64Image = req.body.images[0]; // Take the first image
      }
      // Handle single base64 image
      else if (req.body.image) {
        base64Image = req.body.image;
      }
      else {
        return res.status(400).json({
          error: {
            code: 'NO_IMAGE',
            message: 'No image provided'
          }
        });
      }

      // Get comprehensive multi-stage prompt
      const prompt = promptService.getMultiStagePrompt();

      // Make single API call to Gemini
      const result = await geminiService.analyzeSingleImage(base64Image, prompt);

      logger.info('Single-call multi-stage analysis completed successfully');

      res.json({
        success: true,
        data: result,
        analysisType: 'multi-stage-single-call',
        timestamp: new Date().toISOString()
      });

    } catch (error) {
      logger.error('Error in multi-stage analysis:', error);
      next(error);
    }
  }

  /**
   * Get analysis status (for health checks)
   */
  async getAnalysisStatus(req, res, next) {
    try {
      res.json({
        status: 'operational',
        services: {
          gemini: 'available',
          imageProcessing: 'available'
        },
        limits: {
          maxFileSize: process.env.MAX_FILE_SIZE || '10MB',
          maxFiles: process.env.MAX_FILES || 5,
          timeout: process.env.REQUEST_TIMEOUT || '50000ms'
        },
        timestamp: new Date().toISOString()
      });
    } catch (error) {
      logger.error('Error getting analysis status:', error);
      next(error);
    }
  }

  /**
   * Test endpoint for basic functionality
   */
  async testAnalysis(req, res, next) {
    try {
      // Simple test to verify Gemini service is working
      const testPrompt = "Respond with a simple JSON object: {\"status\": \"working\", \"message\": \"Test successful\"}";

      // Create a simple test image (1x1 pixel white image in base64)
      const testImage = "/9j/4AAQSkZJRgABAQEAYABgAAD/2wBDAAEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQH/2wBDAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQH/wAARCAABAAEDASIAAhEBAxEB/8QAFQABAQAAAAAAAAAAAAAAAAAAAAv/xAAUEAEAAAAAAAAAAAAAAAAAAAAA/8QAFQEBAQAAAAAAAAAAAAAAAAAAAAX/xAAUEQEAAAAAAAAAAAAAAAAAAAAA/9oADAMBAAIRAxEAPwA/8A8A";

      const result = await geminiService.analyzeSingleImage(testImage, testPrompt);

      res.json({
        success: true,
        test: 'passed',
        result: result,
        timestamp: new Date().toISOString()
      });

    } catch (error) {
      logger.error('Error in test analysis:', error);
      res.status(500).json({
        success: false,
        test: 'failed',
        error: error.message,
        timestamp: new Date().toISOString()
      });
    }
  }
}

module.exports = new ImageController();
