# JunkChk Analysis Server

A production-ready Node.js server for analyzing images using Google's Gemini API. This server handles all image processing and AI analysis for the JunkChk app, designed to be deployed on AWS.

## Features

- **Image Analysis**: Single and multiple image analysis using Gemini AI
- **Image Processing**: Automatic image resizing and optimization using Sharp
- **Production Ready**: Comprehensive logging, error handling, and security
- **AWS Deployment**: Configured for AWS deployment with health checks
- **Rate Limiting**: Built-in rate limiting to prevent abuse
- **File Upload**: Support for both file uploads and base64 image data
- **Validation**: Comprehensive input validation and error handling

## API Endpoints

### Image Analysis

- `POST /api/analyze-image` - Analyze single image (file upload)
- `POST /api/analyze-image-base64` - Analyze single image (base64)
- `POST /api/analyze-images` - Analyze multiple images (file upload)
- `POST /api/analyze-images-base64` - Analyze multiple images (base64)

### System

- `GET /health` - Health check endpoint
- `GET /api/status` - API status and service information
- `GET /api/analysis-status` - Analysis service status
- `GET /api/test` - Test analysis functionality

## Installation

1. Clone the repository
2. Install dependencies:
   ```bash
   npm install
   ```

3. Copy environment variables:
   ```bash
   cp .env.example .env
   ```

4. Configure your environment variables in `.env`:
   ```
   GOOGLE_API_KEY=your_google_api_key_here
   PORT=3000
   NODE_ENV=development
   ```

## Usage

### Development
```bash
npm run dev
```

### Production
```bash
npm start
```

### Testing
```bash
npm test
```

## Environment Variables

| Variable | Description | Default |
|----------|-------------|---------|
| `PORT` | Server port | 3000 |
| `NODE_ENV` | Environment | development |
| `GOOGLE_API_KEY` | Google Gemini API key | Required |
| `GEMINI_MODEL` | Gemini model name | gemini-2.5-flash-preview-05-20 |
| `MAX_FILE_SIZE` | Maximum file size in bytes | 10485760 (10MB) |
| `MAX_FILES` | Maximum number of files | 5 |
| `REQUEST_TIMEOUT` | Request timeout in ms | 50000 |
| `RATE_LIMIT_WINDOW_MS` | Rate limit window | 900000 (15 min) |
| `RATE_LIMIT_MAX_REQUESTS` | Max requests per window | 100 |
| `LOG_LEVEL` | Logging level | info |
| `ALLOWED_ORIGINS` | CORS allowed origins | * |

## API Usage Examples

### Single Image Analysis (File Upload)

```javascript
const formData = new FormData();
formData.append('image', imageFile);

const response = await fetch('/api/analyze-image', {
  method: 'POST',
  body: formData
});

const result = await response.json();
```

### Single Image Analysis (Base64)

```javascript
const response = await fetch('/api/analyze-image-base64', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json'
  },
  body: JSON.stringify({
    image: base64ImageString
  })
});

const result = await response.json();
```

### Multiple Images Analysis

```javascript
const formData = new FormData();
imageFiles.forEach(file => {
  formData.append('images', file);
});

const response = await fetch('/api/analyze-images', {
  method: 'POST',
  body: formData
});

const result = await response.json();
```

## Response Format

### Success Response
```json
{
  "success": true,
  "data": {
    "rating": "B",
    "product_details": {
      "name": "Lays Chips Salted",
      "brand": "Lays",
      "model_sku": "...",
      "type": "Chips",
      "use": "Snack",
      "ingredients": [...]
    },
    "explanation": {...},
    "harmful_ingredient_analysis": [...],
    "detailed_rationale": {...}
  },
  "timestamp": "2024-01-01T00:00:00.000Z"
}
```

### Error Response
```json
{
  "error": {
    "code": "ERROR_CODE",
    "message": "Error description"
  },
  "timestamp": "2024-01-01T00:00:00.000Z"
}
```

## Error Codes

- `VALIDATION_ERROR` - Invalid request data
- `NO_FILES` / `NO_IMAGES` - No files/images provided
- `FILE_TOO_LARGE` - File exceeds size limit
- `TOO_MANY_FILES` - Too many files uploaded
- `INVALID_FILE_TYPE` - Unsupported file type
- `TIMEOUT_ERROR` - Request timed out
- `AUTH_ERROR` - Authentication failed
- `QUOTA_ERROR` - API quota exceeded
- `RATE_LIMIT_EXCEEDED` - Rate limit exceeded
- `TECHNICAL_ERROR` - Internal server error

## AWS Deployment

The server is configured for AWS deployment with:

- Health check endpoints for load balancers
- Environment variable configuration
- Proper logging for CloudWatch
- Security headers and CORS configuration
- Rate limiting for production use

### Deployment Steps

1. Set up an EC2 instance or use AWS Lambda
2. Configure environment variables
3. Set up a load balancer with health checks pointing to `/health`
4. Configure CloudWatch for log monitoring
5. Set up auto-scaling based on CPU/memory usage

## Security Features

- Helmet.js for security headers
- CORS configuration
- Rate limiting
- Input validation
- File type and size validation
- Error message sanitization in production

## Logging

The server uses Winston for comprehensive logging:

- All requests are logged with IP and user agent
- Errors are logged with full stack traces
- Separate error and combined log files
- Console logging in development
- Configurable log levels

## Performance

- Image compression and optimization
- Memory-efficient file handling
- Request timeout handling
- Graceful shutdown handling
- Compression middleware for responses

## License

MIT
