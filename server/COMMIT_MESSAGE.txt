feat: Add production-ready JunkChk API server for AWS deployment

- Implement Node.js/Express server with comprehensive Gemini API integration
- Add image processing with Sharp for optimization and validation
- Include production security: rate limiting, CORS, helmet, input validation
- Add comprehensive error handling with proper HTTP status codes
- Implement Winston logging with file rotation and structured logs
- Support both file upload and base64 image analysis endpoints
- Add health check endpoints for AWS load balancer integration
- Include Docker configuration and PM2 process management
- Add comprehensive deployment guide for EC2, ECS, and Lambda options
- Server handles all AI processing, mobile app only sends images
