// PM2 configuration for production deployment
module.exports = {
  apps: [{
    name: 'junkchk-image-server',
    script: 'src/app.js',
    instances: 'max', // Use all available CPU cores
    exec_mode: 'cluster',
    env: {
      NODE_ENV: 'development',
      PORT: 3000
    },
    env_production: {
      NODE_ENV: 'production',
      PORT: 3000
    },
    // Logging
    log_file: 'logs/combined.log',
    out_file: 'logs/out.log',
    error_file: 'logs/error.log',
    log_date_format: 'YYYY-MM-DD HH:mm:ss Z',

    // Auto restart
    watch: false,
    max_memory_restart: '1G',

    // Health monitoring
    min_uptime: '10s',
    max_restarts: 10,

    // Environment variables
    env_file: '.env'
  }]
};
