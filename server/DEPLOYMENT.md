# Gemini Image Analysis Server - AWS Deployment Guide

## 🚀 Production-Ready Server Overview

This server handles all Gemini API interactions for your mobile app, providing a secure and scalable backend solution.

### ✅ Features Implemented

- **Image Analysis**: Single and multiple image analysis using Gemini AI
- **Image Processing**: Automatic resizing and optimization with Sharp
- **Production Security**: Rate limiting, CORS, helmet security headers
- **Error Handling**: Comprehensive error handling with proper HTTP status codes
- **Logging**: <PERSON> logging with file rotation
- **Validation**: Input validation for files and base64 images
- **Health Checks**: Multiple health check endpoints for load balancers
- **AWS Ready**: Docker configuration and PM2 process management

### 📋 API Endpoints

| Method | Endpoint | Description |
|--------|----------|-------------|
| GET | `/health` | Health check for load balancers |
| GET | `/api/status` | API service status |
| GET | `/api/analysis-status` | Analysis service detailed status |
| GET | `/api/test` | Test Gemini functionality |
| POST | `/api/analyze-image` | Single image analysis (file upload) |
| POST | `/api/analyze-image-base64` | Single image analysis (base64) |
| POST | `/api/analyze-images` | Multiple image analysis (file upload) |
| POST | `/api/analyze-images-base64` | Multiple image analysis (base64) |

## 🔧 AWS Deployment Options

### Option 1: EC2 with PM2 (Recommended)

1. **Launch EC2 Instance**
   ```bash
   # Ubuntu 22.04 LTS recommended
   # t3.medium or larger for production
   ```

2. **Install Dependencies**
   ```bash
   # Update system
   sudo apt update && sudo apt upgrade -y

   # Install Node.js 18
   curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
   sudo apt-get install -y nodejs

   # Install PM2 globally
   sudo npm install -g pm2

   # Install nginx (optional, for reverse proxy)
   sudo apt install nginx -y
   ```

3. **Deploy Application**
   ```bash
   # Clone/upload your server code
   cd /opt
   sudo mkdir gemini-server
   sudo chown $USER:$USER gemini-server
   cd gemini-server

   # Copy server files
   # Upload your server directory here

   # Install dependencies
   npm ci --production

   # Set environment variables
   cp .env.example .env
   # Edit .env with your production values
   ```

4. **Configure Environment**
   ```bash
   # Edit .env file
   nano .env
   ```

   Set these production values:
   ```env
   NODE_ENV=production
   PORT=3000
   GOOGLE_API_KEY=your_production_api_key
   TRUST_PROXY=true
   ALLOWED_ORIGINS=https://yourdomain.com
   ```

5. **Start with PM2**
   ```bash
   # Start the application
   pm2 start ecosystem.config.js --env production

   # Save PM2 configuration
   pm2 save

   # Setup PM2 to start on boot
   pm2 startup
   ```

### Option 2: Docker on ECS

1. **Build Docker Image**
   ```bash
   # Build the image
   docker build -t junkchk-image-server .

   # Test locally
   docker run -p 3000:3000 --env-file .env junkchk-image-server
   ```

2. **Push to ECR**
   ```bash
   # Create ECR repository
   aws ecr create-repository --repository-name junkchk-image-server

   # Get login token
   aws ecr get-login-password --region us-east-1 | docker login --username AWS --password-stdin <account-id>.dkr.ecr.us-east-1.amazonaws.com

   # Tag and push
   docker tag junkchk-image-server:latest <account-id>.dkr.ecr.us-east-1.amazonaws.com/junkchk-image-server:latest
   docker push <account-id>.dkr.ecr.us-east-1.amazonaws.com/junkchk-image-server:latest
   ```

3. **Deploy to ECS**
   - Create ECS cluster
   - Create task definition with your image
   - Create service with load balancer
   - Configure health checks to use `/health` endpoint

### Option 3: AWS Lambda (Serverless)

For serverless deployment, you'll need to modify the server slightly:

1. **Install Serverless Framework**
   ```bash
   npm install -g serverless
   npm install serverless-http
   ```

2. **Create serverless.yml** (additional configuration needed)

## 🔒 Security Configuration

### Environment Variables

```env
# Required
GOOGLE_API_KEY=your_google_api_key
NODE_ENV=production

# Security
TRUST_PROXY=true
ALLOWED_ORIGINS=https://yourdomain.com,https://app.yourdomain.com

# Rate Limiting
RATE_LIMIT_WINDOW_MS=900000  # 15 minutes
RATE_LIMIT_MAX_REQUESTS=100  # per IP

# File Limits
MAX_FILE_SIZE=********  # 10MB
MAX_FILES=5
REQUEST_TIMEOUT=50000   # 50 seconds
```

### Nginx Configuration (Optional)

```nginx
server {
    listen 80;
    server_name your-api-domain.com;

    location / {
        proxy_pass http://localhost:3000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
    }
}
```

## 📊 Monitoring

### Health Checks

- **Load Balancer**: Use `/health` endpoint
- **Application**: Use `/api/status` for detailed status
- **Gemini Service**: Use `/api/analysis-status`

### Logs

Logs are written to:
- `logs/combined.log` - All logs
- `logs/error.log` - Error logs only

### Metrics to Monitor

- Response time
- Error rate
- Memory usage
- CPU usage
- Request volume
- Gemini API quota usage

## 🔄 Mobile App Integration

Update your mobile app to use the server:

```javascript
// Replace direct Gemini API calls with server calls
const analyzeImage = async (base64Image) => {
  const response = await fetch('https://your-server.com/api/analyze-image-base64', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    body: JSON.stringify({
      images: [base64Image]
    })
  });

  return await response.json();
};
```

## 🚨 Troubleshooting

### Common Issues

1. **Port 3000 already in use**
   ```bash
   sudo lsof -i :3000
   sudo kill -9 <PID>
   ```

2. **Permission denied**
   ```bash
   sudo chown -R $USER:$USER /opt/junkchk-server
   ```

3. **Out of memory**
   - Increase EC2 instance size
   - Monitor memory usage with `htop`

### Logs

```bash
# View real-time logs
pm2 logs junkchk-image-server

# View log files
tail -f logs/combined.log
tail -f logs/error.log
```

## 📈 Scaling

- **Horizontal**: Use multiple EC2 instances behind a load balancer
- **Vertical**: Increase instance size for more CPU/memory
- **Auto Scaling**: Configure based on CPU/memory metrics
- **CDN**: Use CloudFront for static assets (if any)

## 🎯 Next Steps

1. Deploy to AWS using your preferred method
2. Update mobile app to use server endpoints
3. Set up monitoring and alerting
4. Configure SSL/TLS certificates
5. Set up CI/CD pipeline for updates

Your server is now production-ready and can handle all Gemini API interactions securely and efficiently!
