{"name": "junkchk-analysis-server", "version": "1.0.0", "description": "Production-ready server for JunkChk image analysis", "main": "src/app.js", "scripts": {"start": "node src/app.js", "dev": "nodemon src/app.js", "test": "jest", "lint": "eslint src/", "build": "echo 'No build step required for Node.js'", "deploy": "npm run lint && npm test && echo 'Ready for deployment'"}, "keywords": ["junkchk", "image-analysis", "api", "aws", "production"], "author": "Your Name", "license": "MIT", "dependencies": {"@google/generative-ai": "^0.21.0", "express": "^4.18.2", "multer": "^1.4.5-lts.1", "sharp": "^0.33.2", "cors": "^2.8.5", "helmet": "^7.1.0", "express-rate-limit": "^7.1.5", "dotenv": "^16.3.1", "winston": "^3.11.0", "joi": "^17.11.0", "compression": "^1.7.4", "express-validator": "^7.0.1"}, "devDependencies": {"nodemon": "^3.0.2", "jest": "^29.7.0", "eslint": "^8.56.0", "supertest": "^6.3.3"}, "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}, "repository": {"type": "git", "url": "your-repo-url"}}