import React, { useEffect } from 'react';
import { View, ActivityIndicator } from 'react-native';
import { useNavigation } from '@react-navigation/native';
import { NativeStackNavigationProp } from '@react-navigation/native-stack';
import { useAuth } from '../context';
import { Colors } from '../theme';
import { RootStackParamList } from './types';

type AuthNavigatorNavigationProp = NativeStackNavigationProp<
  RootStackParamList,
  'AuthNavigator'
>;

/**
 * AuthNavigator component
 *
 * This component checks the authentication state and navigates to the appropriate screen:
 * - If the user is authenticated, navigates to the Home screen (main app entry point)
 * - If the user is not authenticated, navigates to the Welcome screen (start of onboarding)
 * - While checking authentication, shows a loading indicator
 */
const AuthNavigator: React.FC = () => {
  const navigation = useNavigation<AuthNavigatorNavigationProp>();
  const { authState } = useAuth();

  useEffect(() => {
    // Check if authentication state has been loaded
    if (!authState.isLoading) {
      if (authState.isAuthenticated) {
        // User is authenticated, navigate to the main app entry point
        console.log('User is authenticated, navigating to Home screen');
        navigation.reset({
          index: 0,
          routes: [{ name: 'Home' }],
        });
      } else {
        // User is not authenticated, navigate to the onboarding flow
        console.log('User is not authenticated, navigating to Welcome screen');
        navigation.reset({
          index: 0,
          routes: [{ name: 'Welcome' }],
        });
      }
    }
  }, [authState.isLoading, authState.isAuthenticated, navigation]);

  // Show loading indicator while checking authentication state
  return (
    <View style={{ flex: 1, justifyContent: 'center', alignItems: 'center', backgroundColor: Colors.BackgroundPrimary }}>
      <ActivityIndicator size="large" color={Colors.DarkText} />
    </View>
  );
};

export default AuthNavigator;
