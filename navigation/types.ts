// Define the navigation types for the app

export type RootStackParamList = {
  // Auth Navigator
  AuthNavigator: undefined;

  // Main app screens
  Home: undefined;  // Main home screen
  History: undefined;
  ComparisonSelection: { currentProductId?: string };
  ComparisonView: { selectedProductIds: string[] };
  Settings: undefined;
  SettingsAccount: undefined;
  SettingsAllergies: undefined;
  SettingsSubscription: undefined;
  SettingsSupport: undefined;
  SettingsAbout: undefined;

  // Debug/Test screens
  ServerTest: undefined;

  // Core functionality screens
  Scan: { fromMainNav?: boolean };
  Processing: { imageUri: string };
  ScanResults: {
    imageUri: string;
    isAnonymous: boolean;
    apiResponse: any;
    fromHistory?: boolean;
    fromHome?: boolean;
  };
  ScanError: {
    errorCode: string;
    errorMessage: string;
    imageUri?: string;
  };

  // Onboarding screens
  Welcome: undefined;
  StartPersonalization: undefined;
  YourGoals: undefined;
  AllergiesRestrictions: undefined;
  HowYourAnswersHelp: undefined;
  HowJunkChkWorks: undefined;

  // Account creation screens
  TermsPrivacy: { imageUri: string };
  PhoneNumberLogin: { imageUri: string };
  OTPVerification: { phoneNumber: string };
  CreatingAccount: undefined;
  AccountCreated: undefined;
};
