import { Platform } from 'react-native';

export const Colors = {
  // Primary Colors
  BackgroundPrimary: '#FFFFFF', // Pure White - Main background for screens and cards
  DarkText: '#1A1A1A', // Very Dark Gray/Near Black - Primary text color, button backgrounds, primary icons
  LightText: '#7F7F7F', // Medium Gray - Secondary text, descriptions, labels, subtle hints
  SurfaceSecondary: '#F5F5F5', // Very Light Gray - Subtle backgrounds, dividers, input field fills

  // Accent / Status Colors
  Success: '#00CC00', // Bright Green - Used for success states, positive results (Grade A, good ingredients), checkmarks
  Error: '#FF0000', // Bright Red - Used for error states, warnings, negative results (Grade E, bad ingredients), delete actions
  Warning: '#FFA500', // Orange - Used for warning states, intermediate negative results (Grade D)
  Info: '#FFD700', // Gold/Yellow - Used for intermediate results (Grade C)
  AccentBlue: '#007AFF', // Standard iOS Blue - Used for interactive links

  // Grade Specific Colors
  GradeA: '#00CC00', // Same as Success
  GradeB: '#2ECC71', // Slightly less vibrant green
  GradeC: '#FFD700', // Same as Info
  GradeD: '#FFA500', // Same as Warning
  GradeE: '#FF0000', // Same as Error
};

export const Typography = {
  // Font Family
  FontFamily: 'Poppins', // Using Poppins font for the entire app

  // Text Styles
  Heading1: {
    fontFamily: 'Poppins-Bold', // Bold for main headings for maximum visibility
    fontSize: 28,
    fontWeight: '700', // Bold
    lineHeight: 34,
    color: Colors.DarkText,
  },
  Heading2: {
    fontFamily: 'Poppins-SemiBold', // SemiBold for secondary headings
    fontSize: 20,
    fontWeight: '600', // SemiBold
    lineHeight: 24,
    color: Colors.DarkText,
  },
  BodyText: {
    fontFamily: 'Poppins', // Regular for body text
    fontSize: 16,
    fontWeight: '400', // Regular
    lineHeight: 22,
    color: Colors.DarkText,
  },
  Description: {
    fontFamily: 'Poppins', // Regular for descriptions
    fontSize: 14,
    fontWeight: '400', // Regular
    lineHeight: 20,
    color: Colors.LightText,
  },
  Caption: {
    fontFamily: 'Poppins', // Regular for captions
    fontSize: 12,
    fontWeight: '400', // Regular
    lineHeight: 16,
    color: Colors.LightText,
  },
  ButtonText: {
    fontFamily: 'Poppins-SemiBold', // SemiBold for button text for better readability
    fontSize: 16,
    fontWeight: '600', // SemiBold
    lineHeight: 22,
    color: Colors.BackgroundPrimary, // For primary buttons (dark background)
  },
};

export const Spacing = {
  // Base Unit
  Unit: 8,

  // Scale
  ExtraSmall: 4, // Unit * 0.5
  Small: 8, // Unit * 1
  Medium: 16, // Unit * 2
  Large: 24, // Unit * 3
  ExtraLarge: 32, // Unit * 4
  XXLarge: 48, // Unit * 6
  XXXLarge: 64, // Unit * 8
};

export const BorderRadius = {
  Small: 8,
  Medium: 12,
  Large: 16,
  ExtraLarge: 24,
  Round: 9999, // For circular elements
};

// Create shadow objects with proper platform-specific configurations
const createShadow = (iosConfig: any, androidConfig: any) => {
  if (Platform.OS === 'ios') {
    return iosConfig;
  } else {
    return androidConfig;
  }
};

export const Shadow = {
  Small: createShadow(
    {
      shadowColor: '#000',
      shadowOffset: { width: 0, height: 2 },
      shadowOpacity: 0.1,
      shadowRadius: 4,
    },
    {
      shadowColor: '#000',
      shadowOpacity: 0.1,
      shadowRadius: 4,
      elevation: 2,
    }
  ),
  Medium: createShadow(
    {
      shadowColor: '#000',
      shadowOffset: { width: 0, height: 4 },
      shadowOpacity: 0.15,
      shadowRadius: 8,
    },
    {
      shadowColor: '#000',
      shadowOpacity: 0.15,
      shadowRadius: 8,
      elevation: 4,
    }
  ),
  Large: createShadow(
    {
      shadowColor: '#000',
      shadowOffset: { width: 0, height: 8 },
      shadowOpacity: 0.2,
      shadowRadius: 12,
    },
    {
      shadowColor: '#000',
      shadowOpacity: 0.2,
      shadowRadius: 12,
      elevation: 8,
    }
  ),
};

export default {
  Colors,
  Typography,
  Spacing,
  BorderRadius,
  Shadow,
};
