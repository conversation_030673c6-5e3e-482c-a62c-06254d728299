// Authentication State Types
export interface User {
  id: string;
  phoneNumber: string;
  createdAt: string;
}

export interface AuthState {
  isAuthenticated: boolean;
  user: User | null;
  isLoading: boolean;
  error: string | null;
}

// User Personalization Types
export type Goal = 
  | 'To identify allergens/intolerances'
  | 'To understand ingredient lists'
  | 'To find healthier options'
  | 'To track product information'
  | 'Other';

export interface PersonalizationState {
  goal: Goal | null;
  allergiesRestrictions: string[];
  isPersonalizationComplete: boolean;
}

// Scan Data Types
export interface Ingredient {
  name: string;
  description: string;
}

export interface AllergyWarning {
  ingredient: string;
  message: string;
}

export interface Warning {
  type: string;
  message: string;
}

export type Grade = 'A' | 'B' | 'C' | 'D' | 'E';

export interface Effects {
  positive_effects: {
    short_term: string[];
    long_term: string[];
  };
  negative_effects: {
    short_term: string[];
    long_term: string[];
  };
}

export interface InfoCard {
  title: string;
  value: string;
  icon: string;
  color: string;
  priority?: string;
}

export interface ScanResult {
  id: string;
  imageUri: string;
  timestamp: string;
  productName: string;
  grade: Grade;
  summary: string;
  goodIngredients: Ingredient[];
  badIngredients: Ingredient[];
  allergyWarnings: AllergyWarning[];
  warnings: Warning[];
  explanation: string;
  effects?: Effects;
  infoCards?: InfoCard[];
}

export interface ScanState {
  currentScan: ScanResult | null;
  isScanning: boolean;
  error: string | null;
}

// History State Types
export interface HistoryState {
  scans: ScanResult[];
  isLoading: boolean;
  error: string | null;
}

// Subscription State Types
export type SubscriptionTier = 'free' | 'premium';

export interface SubscriptionState {
  tier: SubscriptionTier;
  expiresAt: string | null;
  features: string[];
  isLoading: boolean;
  error: string | null;
}

// Combined App State
export interface AppState {
  auth: AuthState;
  personalization: PersonalizationState;
  scan: ScanState;
  history: HistoryState;
  subscription: SubscriptionState;
}
