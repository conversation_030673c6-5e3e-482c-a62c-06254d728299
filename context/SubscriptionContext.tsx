import React, { createContext, useContext, useState, useEffect, ReactNode, useMemo, useCallback } from 'react';
import { SubscriptionState, SubscriptionTier } from './types';
import { STORAGE_KEYS, storeAuthAwareData, getAuthAwareData } from './storage';
import { useAuth } from './AuthContext';

// Default subscription state
const defaultSubscriptionState: SubscriptionState = {
  tier: 'free',
  expiresAt: null,
  features: [
    'Basic product scanning',
    'Allergen identification',
    'Basic ingredient analysis',
  ],
  isLoading: false,
  error: null,
};

// Premium features
const premiumFeatures = [
  'Basic product scanning',
  'Allergen identification',
  'Advanced ingredient analysis',
  'Unlimited scan history',
  'Product comparisons',
  'Personalized recommendations',
  'Export reports',
];

// Create the context
export const SubscriptionContext = createContext<{
  subscriptionState: SubscriptionState;
  upgradeToPremium: () => Promise<void>;
  downgradeToFree: () => Promise<void>;
  clearError: () => void;
}>({
  subscriptionState: defaultSubscriptionState,
  upgradeToPremium: async () => {},
  downgradeToFree: async () => {},
  clearError: () => {},
});

// Custom hook to use the subscription context
export const useSubscription = () => useContext(SubscriptionContext);

// Provider component
export const SubscriptionProvider: React.FC<{ children: ReactNode }> = ({ children }) => {
  const [subscriptionState, setSubscriptionState] = useState<SubscriptionState>(defaultSubscriptionState);
  const { authState } = useAuth();

  // Load subscription state from storage on mount and when auth state changes
  useEffect(() => {
    const loadSubscriptionState = async () => {
      try {
        setSubscriptionState({ ...subscriptionState, isLoading: true });

        // Use authentication-aware storage
        const storedState = await getAuthAwareData<SubscriptionState>(
          authState.isAuthenticated,
          STORAGE_KEYS.LEGACY.SUBSCRIPTION, // Use legacy key for anonymous users
          STORAGE_KEYS.AUTHENTICATED.SUBSCRIPTION
        );

        if (storedState) {
          setSubscriptionState({
            ...storedState,
            isLoading: false,
          });
        } else {
          setSubscriptionState({
            ...defaultSubscriptionState,
            isLoading: false,
          });
        }
      } catch (error) {
        console.error('Error loading subscription state:', error);
        setSubscriptionState({
          ...defaultSubscriptionState,
          isLoading: false,
          error: 'Failed to load subscription information.',
        });
      }
    };

    loadSubscriptionState();
  }, [authState.isAuthenticated]); // Reload when auth state changes

  // Upgrade to premium function - memoized
  const upgradeToPremium = useCallback(async (): Promise<void> => {
    try {
      setSubscriptionState(prevState => ({ ...prevState, isLoading: true }));

      // In a real app, you would make an API call to process payment
      // For now, we'll just simulate a successful upgrade

      // Calculate expiration date (1 year from now)
      const expirationDate = new Date();
      expirationDate.setFullYear(expirationDate.getFullYear() + 1);

      const newState: SubscriptionState = {
        tier: 'premium',
        expiresAt: expirationDate.toISOString(),
        features: premiumFeatures,
        isLoading: false,
        error: null,
      };

      // Save to authentication-aware storage
      await storeAuthAwareData(
        authState.isAuthenticated,
        STORAGE_KEYS.LEGACY.SUBSCRIPTION, // Use legacy key for anonymous users
        STORAGE_KEYS.AUTHENTICATED.SUBSCRIPTION,
        newState
      );

      // Update state
      setSubscriptionState(newState);
    } catch (error) {
      setSubscriptionState(prevState => ({
        ...prevState,
        isLoading: false,
        error: 'Failed to upgrade subscription. Please try again.',
      }));
    }
  }, [authState.isAuthenticated]);

  // Downgrade to free function - memoized
  const downgradeToFree = useCallback(async (): Promise<void> => {
    try {
      setSubscriptionState(prevState => ({ ...prevState, isLoading: true }));

      // In a real app, you would make an API call to cancel subscription
      // For now, we'll just simulate a successful downgrade

      const newState: SubscriptionState = {
        ...defaultSubscriptionState,
        isLoading: false,
      };

      // Save to authentication-aware storage
      await storeAuthAwareData(
        authState.isAuthenticated,
        STORAGE_KEYS.LEGACY.SUBSCRIPTION, // Use legacy key for anonymous users
        STORAGE_KEYS.AUTHENTICATED.SUBSCRIPTION,
        newState
      );

      // Update state
      setSubscriptionState(newState);
    } catch (error) {
      setSubscriptionState(prevState => ({
        ...prevState,
        isLoading: false,
        error: 'Failed to downgrade subscription. Please try again.',
      }));
    }
  }, [authState.isAuthenticated]);

  // Clear error function - memoized
  const clearError = useCallback(() => {
    setSubscriptionState(prevState => ({ ...prevState, error: null }));
  }, []);

  // Memoize the context value to prevent unnecessary re-renders
  const contextValue = useMemo(() => ({
    subscriptionState,
    upgradeToPremium,
    downgradeToFree,
    clearError,
  }), [subscriptionState, upgradeToPremium, downgradeToFree, clearError]);

  return (
    <SubscriptionContext.Provider value={contextValue}>
      {children}
    </SubscriptionContext.Provider>
  );
};
