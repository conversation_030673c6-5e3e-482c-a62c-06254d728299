import AsyncStorage from '@react-native-async-storage/async-storage';
import { SafeAsyncStorage, UnifiedStorage } from '../utils/SafeStorage';

// Storage Keys with separate namespaces for anonymous and authenticated data
export const STORAGE_KEYS = {
  // Authentication data
  AUTH: '@JunkChk:auth',

  // Device-specific data (persists across logins)
  PERSONALIZATION: '@JunkChk:personalization',
  APP_SETTINGS: '@JunkChk:appSettings',

  // Anonymous user data
  ANONYMOUS: {
    CURRENT_SCAN: '@JunkChk:anonymous:currentScan',
    HISTORY: '@JunkChk:anonymous:history',
  },

  // Authenticated user data
  AUTHENTICATED: {
    CURRENT_SCAN: '@JunkChk:authenticated:currentScan',
    HISTORY: '@JunkChk:authenticated:history',
    SUBSCRIPTION: '@JunkChk:authenticated:subscription',
  },

  // Legacy keys (for migration)
  LEGACY: {
    CURRENT_SCAN: '@JunkChk:currentScan',
    HISTORY: '@JunkChk:history',
    SUBSCRIPTION: '@JunkChk:subscription',
  }
};

// Generic storage functions with enhanced safety
export const storeData = async <T>(key: string, value: T): Promise<void> => {
  try {
    // Validate key parameter
    if (!key || typeof key !== 'string') {
      throw new Error(`Invalid storage key: ${key}. Key must be a non-empty string.`);
    }

    const jsonValue = JSON.stringify(value);
    const success = await UnifiedStorage.setItem(key, jsonValue);

    if (!success) {
      throw new Error(`Failed to store data for key: ${key}`);
    }
  } catch (error) {
    console.error(`Error storing data for key ${key}:`, error);
    throw error;
  }
};

export const getData = async <T>(key: string): Promise<T | null> => {
  try {
    // Validate key parameter
    if (!key || typeof key !== 'string') {
      console.error(`Invalid storage key: ${key}. Key must be a non-empty string.`);
      return null;
    }

    const jsonValue = await UnifiedStorage.getItem(key);
    return jsonValue != null ? JSON.parse(jsonValue) : null;
  } catch (error) {
    console.error(`Error retrieving data for key ${key}:`, error);
    return null;
  }
};

export const removeData = async (key: string): Promise<void> => {
  try {
    // Validate key parameter
    if (!key || typeof key !== 'string') {
      throw new Error(`Invalid storage key: ${key}. Key must be a non-empty string.`);
    }

    const success = await UnifiedStorage.removeItem(key);

    if (!success) {
      console.warn(`Failed to remove data for key: ${key}`);
    }
  } catch (error) {
    console.error(`Error removing data for key ${key}:`, error);
    throw error;
  }
};

// Authentication-aware storage functions
export const getAuthAwareKey = (isAuthenticated: boolean, anonymousKey: string, authenticatedKey: string): string => {
  // Validate key parameters
  if (!anonymousKey || typeof anonymousKey !== 'string') {
    throw new Error(`Invalid anonymous key: ${anonymousKey}. Key must be a non-empty string.`);
  }
  if (!authenticatedKey || typeof authenticatedKey !== 'string') {
    throw new Error(`Invalid authenticated key: ${authenticatedKey}. Key must be a non-empty string.`);
  }

  return isAuthenticated ? authenticatedKey : anonymousKey;
};

// Store data based on authentication state
export const storeAuthAwareData = async <T>(
  isAuthenticated: boolean,
  anonymousKey: string,
  authenticatedKey: string,
  value: T
): Promise<void> => {
  const key = getAuthAwareKey(isAuthenticated, anonymousKey, authenticatedKey);
  await storeData(key, value);
};

// Get data based on authentication state
export const getAuthAwareData = async <T>(
  isAuthenticated: boolean,
  anonymousKey: string,
  authenticatedKey: string
): Promise<T | null> => {
  const key = getAuthAwareKey(isAuthenticated, anonymousKey, authenticatedKey);
  return await getData<T>(key);
};

// Migration utilities
export const migrateAnonymousToAuthenticated = async <T>(
  anonymousKey: string,
  authenticatedKey: string,
  mergeFunction?: (authenticatedData: T | null, anonymousData: T | null) => T | null
): Promise<void> => {
  try {
    // Get anonymous data
    const anonymousData = await getData<T>(anonymousKey);

    // If no anonymous data, nothing to migrate
    if (!anonymousData) {
      return;
    }

    // Get existing authenticated data (if any)
    const authenticatedData = await getData<T>(authenticatedKey);

    // Merge data if a merge function is provided, otherwise use anonymous data
    const mergedData = mergeFunction
      ? mergeFunction(authenticatedData, anonymousData)
      : anonymousData;

    // Store merged data in authenticated storage
    if (mergedData) {
      await storeData(authenticatedKey, mergedData);
    }

    // Clear anonymous data after successful migration
    await removeData(anonymousKey);
  } catch (error) {
    console.error(`Error migrating data from ${anonymousKey} to ${authenticatedKey}:`, error);
    throw error;
  }
};

// Check for and migrate legacy data
export const migrateLegacyData = async (): Promise<void> => {
  try {
    // Check if we have legacy data
    const hasLegacyHistory = await AsyncStorage.getItem(STORAGE_KEYS.LEGACY.HISTORY) !== null;
    const hasLegacyScan = await AsyncStorage.getItem(STORAGE_KEYS.LEGACY.CURRENT_SCAN) !== null;

    if (hasLegacyHistory) {
      // Move legacy history to anonymous history
      const legacyHistory = await getData(STORAGE_KEYS.LEGACY.HISTORY);
      if (legacyHistory) {
        await storeData(STORAGE_KEYS.ANONYMOUS.HISTORY, legacyHistory);
        await removeData(STORAGE_KEYS.LEGACY.HISTORY);
      }
    }

    if (hasLegacyScan) {
      // Move legacy scan to anonymous scan
      const legacyScan = await getData(STORAGE_KEYS.LEGACY.CURRENT_SCAN);
      if (legacyScan) {
        await storeData(STORAGE_KEYS.ANONYMOUS.CURRENT_SCAN, legacyScan);
        await removeData(STORAGE_KEYS.LEGACY.CURRENT_SCAN);
      }
    }
  } catch (error) {
    console.error('Error migrating legacy data:', error);
    // Don't throw here, just log the error and continue
  }
};

export const clearAllData = async (): Promise<void> => {
  try {
    await AsyncStorage.clear();
  } catch (error) {
    console.error('Error clearing all data:', error);
    throw error;
  }
};
