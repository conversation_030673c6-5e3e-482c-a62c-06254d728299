import React, { ReactNode } from 'react';
import { AuthProvider } from './AuthContext';
import { PersonalizationProvider } from './PersonalizationContext';
import { ScanProvider } from './ScanContext';
import { HistoryProvider } from './HistoryContext';
import { SubscriptionProvider } from './SubscriptionContext';

interface AppProviderProps {
  children: ReactNode;
}

export const AppProvider: React.FC<AppProviderProps> = ({ children }) => {
  return (
    <AuthProvider>
      <PersonalizationProvider>
        <ScanProvider>
          <HistoryProvider>
            <SubscriptionProvider>
              {children}
            </SubscriptionProvider>
          </HistoryProvider>
        </ScanProvider>
      </PersonalizationProvider>
    </AuthProvider>
  );
};

export default AppProvider;
