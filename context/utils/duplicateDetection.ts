import { ScanResult } from '../types';

/**
 * Utility functions for detecting duplicate scans
 */

/**
 * Check if a scan already exists in a list of scans
 * @param newScan - The scan to check for duplicates
 * @param existingScans - The list of existing scans to check against
 * @param timeThresholdMs - Time threshold in milliseconds to consider scans as duplicates (default: 30 seconds)
 * @returns true if the scan is a duplicate, false otherwise
 */
export const isDuplicateScan = (
  newScan: ScanResult, 
  existingScans: ScanResult[], 
  timeThresholdMs: number = 30000
): boolean => {
  return existingScans.some(existingScan => {
    // Check for exact ID match first
    if (existingScan.id === newScan.id) {
      return true;
    }
    
    // Check for duplicate based on product name, image URI, and timestamp
    // This handles cases where the same scan might be added with different IDs
    const isSameProduct = existingScan.productName.toLowerCase() === newScan.productName.toLowerCase();
    const isSameImage = existingScan.imageUri === newScan.imageUri;
    
    // Consider scans as duplicates if they're for the same product and image
    // and within the specified time threshold
    if (isSameProduct && isSameImage) {
      const existingTime = new Date(existingScan.timestamp).getTime();
      const newTime = new Date(newScan.timestamp).getTime();
      const timeDifference = Math.abs(existingTime - newTime);
      
      // If within time threshold, consider it a duplicate
      return timeDifference < timeThresholdMs;
    }
    
    return false;
  });
};

/**
 * Remove duplicates from a list of scans, keeping the most recent one for each unique scan
 * @param scans - The list of scans to deduplicate
 * @param timeThresholdMs - Time threshold in milliseconds to consider scans as duplicates (default: 30 seconds)
 * @returns A deduplicated list of scans
 */
export const removeDuplicateScans = (
  scans: ScanResult[], 
  timeThresholdMs: number = 30000
): ScanResult[] => {
  const uniqueScans: ScanResult[] = [];
  
  scans.forEach(scan => {
    if (!isDuplicateScan(scan, uniqueScans, timeThresholdMs)) {
      uniqueScans.push(scan);
    }
  });
  
  // Sort by timestamp (newest first)
  return uniqueScans.sort((a, b) => 
    new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime()
  );
};

/**
 * Create a unique key for a scan based on product name and image URI
 * @param scan - The scan to create a key for
 * @returns A unique string key
 */
export const createScanKey = (scan: ScanResult): string => {
  const productNameLower = scan.productName.toLowerCase();
  return `${productNameLower}-${scan.imageUri}`;
};

/**
 * Group scans by their unique key (product name + image URI)
 * @param scans - The list of scans to group
 * @returns A map of scan keys to arrays of scans
 */
export const groupScansByKey = (scans: ScanResult[]): Record<string, ScanResult[]> => {
  const groups: Record<string, ScanResult[]> = {};
  
  scans.forEach(scan => {
    const key = createScanKey(scan);
    if (!groups[key]) {
      groups[key] = [];
    }
    groups[key].push(scan);
  });
  
  return groups;
};
