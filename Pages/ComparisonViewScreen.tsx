import React, { useState, useEffect, useMemo } from 'react';
import { View, StyleSheet, ScrollView, Dimensions } from 'react-native';
import { useNavigation, useRoute } from '@react-navigation/native';
import { NativeStackNavigationProp } from '@react-navigation/native-stack';
import { Colors, Spacing, Shadow } from '../theme';
import Screen from '../Components/Screen';
import Typography from '../Components/Typography';
import BackButton from '../Components/BackButton';
import { Ionicons } from '@expo/vector-icons';
import { useHistory, ScanResult, Ingredient } from '../context';

// Define the navigation param list
type RootStackParamList = {
  History: undefined;
  ComparisonSelection: { currentProductId?: string };
  ComparisonView: { selectedProductIds: string[] };
};

type ComparisonViewScreenNavigationProp = NativeStackNavigationProp<
  RootStackParamList,
  'ComparisonView'
>;

type RouteParams = {
  selectedProductIds: string[];
};

// Define types for our comparison data
interface IngredientComparison {
  name: string;
  isGood: boolean;
  products: {
    [productId: string]: {
      exists: boolean;
      description: string;
    }
  };
}

interface ImpactComparison {
  impact: string;
  type: 'short-term' | 'long-term';
  products: {
    [productId: string]: boolean;
  };
}

// Helper function to extract impacts from ingredient descriptions
const extractImpacts = (ingredients: Ingredient[]): { shortTerm: string[], longTerm: string[] } => {
  const shortTermKeywords = ['immediate', 'short-term', 'acute', 'quickly', 'rapid', 'instant'];
  const longTermKeywords = ['long-term', 'chronic', 'prolonged', 'extended', 'over time', 'continuous'];

  const shortTerm: string[] = [];
  const longTerm: string[] = [];

  ingredients.forEach(ingredient => {
    const description = ingredient.description.toLowerCase();

    // Check for short-term impacts
    if (shortTermKeywords.some(keyword => description.includes(keyword))) {
      shortTerm.push(`${ingredient.name}: ${ingredient.description}`);
    }

    // Check for long-term impacts
    if (longTermKeywords.some(keyword => description.includes(keyword))) {
      longTerm.push(`${ingredient.name}: ${ingredient.description}`);
    }
  });

  // If we couldn't extract specific impacts, use some generic ones based on bad ingredients
  if (shortTerm.length === 0 && ingredients.length > 0) {
    shortTerm.push(`May cause immediate discomfort or allergic reactions`);
    shortTerm.push(`Potential digestive issues or skin irritation`);
  }

  if (longTerm.length === 0 && ingredients.length > 0) {
    longTerm.push(`Potential contribution to health issues with regular consumption`);
    longTerm.push(`May have cumulative effects over time`);
  }

  return { shortTerm, longTerm };
};

const ComparisonViewScreen: React.FC = () => {
  const navigation = useNavigation<ComparisonViewScreenNavigationProp>();
  const route = useRoute();
  const { selectedProductIds } = (route.params as RouteParams) || { selectedProductIds: [] };
  const { historyState } = useHistory();
  const screenWidth = Dimensions.get('window').width;

  // Get selected products from history
  const selectedProducts = historyState.scans.filter(scan =>
    selectedProductIds.includes(scan.id)
  );

  // Get grade color based on grade
  const getGradeColor = (grade: 'A' | 'B' | 'C' | 'D' | 'E') => {
    switch (grade) {
      case 'A':
        return Colors.GradeA;
      case 'B':
        return Colors.GradeB;
      case 'C':
        return Colors.GradeC;
      case 'D':
        return Colors.GradeD;
      case 'E':
        return Colors.GradeE;
      default:
        return Colors.LightText;
    }
  };

  // Handle back button press
  const handleBack = () => {
    navigation.goBack();
  };

  // Calculate column width based on number of products
  const columnWidth = useMemo(() => {
    const totalProducts = selectedProducts.length;
    // First column is for labels (30% of screen width)
    const labelColumnWidth = screenWidth * 0.3;
    // Remaining width divided by number of products
    const productColumnWidth = (screenWidth - labelColumnWidth - (Spacing.Medium * 2)) / totalProducts;
    return { labelColumnWidth, productColumnWidth };
  }, [selectedProducts.length, screenWidth]);

  // Process ingredients for comparison
  const ingredientComparisons = useMemo(() => {
    const allIngredients: IngredientComparison[] = [];
    const ingredientMap = new Map<string, IngredientComparison>();

    // First, collect all unique ingredients
    selectedProducts.forEach(product => {
      // Process bad ingredients first (prioritize)
      product.badIngredients.forEach(ingredient => {
        const lowerName = ingredient.name.toLowerCase();
        if (!ingredientMap.has(lowerName)) {
          ingredientMap.set(lowerName, {
            name: ingredient.name,
            isGood: false,
            products: {}
          });
        }

        // Mark this ingredient as existing in this product
        const comparison = ingredientMap.get(lowerName)!;
        comparison.products[product.id] = {
          exists: true,
          description: ingredient.description
        };
      });

      // Then process good ingredients
      product.goodIngredients.forEach(ingredient => {
        const lowerName = ingredient.name.toLowerCase();
        if (!ingredientMap.has(lowerName)) {
          ingredientMap.set(lowerName, {
            name: ingredient.name,
            isGood: true,
            products: {}
          });
        }

        // Mark this ingredient as existing in this product
        const comparison = ingredientMap.get(lowerName)!;
        comparison.products[product.id] = {
          exists: true,
          description: ingredient.description
        };
      });
    });

    // Fill in missing products with exists: false
    ingredientMap.forEach(comparison => {
      selectedProducts.forEach(product => {
        if (!comparison.products[product.id]) {
          comparison.products[product.id] = {
            exists: false,
            description: ''
          };
        }
      });
    });

    // Convert map to array and sort (bad ingredients first, then alphabetically)
    return Array.from(ingredientMap.values()).sort((a, b) => {
      if (a.isGood !== b.isGood) {
        return a.isGood ? 1 : -1; // Bad ingredients first
      }
      return a.name.localeCompare(b.name); // Then alphabetically
    });
  }, [selectedProducts]);

  // Process impacts for comparison
  const impactComparisons = useMemo(() => {
    const shortTermImpacts: ImpactComparison[] = [];
    const longTermImpacts: ImpactComparison[] = [];

    selectedProducts.forEach(product => {
      // Extract impacts from bad ingredients
      const { shortTerm, longTerm } = extractImpacts(product.badIngredients);

      // Process short-term impacts
      shortTerm.forEach(impact => {
        const existingImpact = shortTermImpacts.find(i => i.impact === impact);
        if (existingImpact) {
          existingImpact.products[product.id] = true;
        } else {
          const newImpact: ImpactComparison = {
            impact,
            type: 'short-term',
            products: {}
          };
          newImpact.products[product.id] = true;
          shortTermImpacts.push(newImpact);
        }
      });

      // Process long-term impacts
      longTerm.forEach(impact => {
        const existingImpact = longTermImpacts.find(i => i.impact === impact);
        if (existingImpact) {
          existingImpact.products[product.id] = true;
        } else {
          const newImpact: ImpactComparison = {
            impact,
            type: 'long-term',
            products: {}
          };
          newImpact.products[product.id] = true;
          longTermImpacts.push(newImpact);
        }
      });
    });

    // Fill in missing products with false
    [...shortTermImpacts, ...longTermImpacts].forEach(impact => {
      selectedProducts.forEach(product => {
        if (impact.products[product.id] === undefined) {
          impact.products[product.id] = false;
        }
      });
    });

    return { shortTermImpacts, longTermImpacts };
  }, [selectedProducts]);

  // Render the header with product names and grades
  const renderHeader = () => (
    <View style={[styles.tableHeader, Shadow.Small]}>
      <View style={[styles.labelColumn, { width: columnWidth.labelColumnWidth }]}>
        <Typography variant="bodyText" style={styles.labelText}>
          Product
        </Typography>
      </View>
      {selectedProducts.map(product => (
        <View
          key={product.id}
          style={[styles.productColumn, { width: columnWidth.productColumnWidth }]}
        >
          <View
            style={[
              styles.gradeContainer,
              { backgroundColor: getGradeColor(product.grade) }
            ]}
          >
            <Typography
              variant="heading2"
              color={Colors.BackgroundPrimary}
              style={styles.gradeText}
            >
              {product.grade}
            </Typography>
          </View>
          <Typography variant="bodyText" style={styles.productName} numberOfLines={2}>
            {product.productName}
          </Typography>
        </View>
      ))}
    </View>
  );

  // Render a section header
  const renderSectionHeader = (title: string) => (
    <View style={styles.sectionHeader}>
      <Typography variant="heading2" style={styles.sectionTitle}>
        {title}
      </Typography>
    </View>
  );

  // Render an ingredient row
  const renderIngredientRow = (ingredient: IngredientComparison) => (
    <View style={styles.tableRow} key={ingredient.name}>
      <View style={[styles.labelColumn, { width: columnWidth.labelColumnWidth }]}>
        <View style={styles.ingredientLabelContainer}>
          <Ionicons
            name={ingredient.isGood ? "checkmark-circle" : "alert-circle"}
            size={16}
            color={ingredient.isGood ? Colors.Success : Colors.Error}
            style={styles.ingredientIcon}
          />
          <Typography
            variant="bodyText"
            style={[
              styles.ingredientName,
              ingredient.isGood ? styles.goodIngredient : styles.badIngredient
            ]}
          >
            {ingredient.name}
          </Typography>
        </View>
      </View>

      {selectedProducts.map(product => (
        <View
          key={product.id}
          style={[
            styles.productColumn,
            { width: columnWidth.productColumnWidth }
          ]}
        >
          {ingredient.products[product.id].exists ? (
            <View style={styles.ingredientExists}>
              <Ionicons
                name={ingredient.isGood ? "checkmark-circle" : "alert-circle"}
                size={16}
                color={ingredient.isGood ? Colors.Success : Colors.Error}
              />
              {ingredient.products[product.id].description ? (
                <Typography
                  variant="description"
                  style={styles.ingredientDescription}
                  numberOfLines={2}
                >
                  {ingredient.products[product.id].description}
                </Typography>
              ) : null}
            </View>
          ) : (
            <Typography variant="description" style={styles.notPresent}>
              Not present
            </Typography>
          )}
        </View>
      ))}
    </View>
  );

  // Render an impact row
  const renderImpactRow = (impact: ImpactComparison) => (
    <View style={styles.tableRow} key={impact.impact}>
      <View style={[styles.labelColumn, { width: columnWidth.labelColumnWidth }]}>
        <Typography variant="bodyText" style={styles.impactText}>
          {impact.impact}
        </Typography>
      </View>

      {selectedProducts.map(product => (
        <View
          key={product.id}
          style={[
            styles.productColumn,
            { width: columnWidth.productColumnWidth }
          ]}
        >
          {impact.products[product.id] ? (
            <Ionicons
              name="checkmark-circle"
              size={16}
              color={Colors.Error}
              style={styles.impactIcon}
            />
          ) : (
            <Typography variant="description" style={styles.notPresent}>
              Not applicable
            </Typography>
          )}
        </View>
      ))}
    </View>
  );

  return (
    <Screen scrollable={false} style={styles.screen}>
      <View style={styles.header}>
        <BackButton onPress={handleBack} />
        <Typography variant="heading1" style={styles.title}>
          Product Comparison
        </Typography>
      </View>

      <View style={styles.tableContainer}>
        {/* Fixed header */}
        {renderHeader()}

        <ScrollView
          style={styles.scrollView}
          contentContainerStyle={styles.scrollContent}
          showsVerticalScrollIndicator={false}
        >
          {/* Concerning Ingredients Section */}
          {ingredientComparisons.some(ingredient => !ingredient.isGood) && (
            <>
              {renderSectionHeader('Concerning Ingredients')}
              {ingredientComparisons
                .filter(ingredient => !ingredient.isGood)
                .map(renderIngredientRow)}
            </>
          )}

          {/* Good Ingredients Section */}
          {ingredientComparisons.some(ingredient => ingredient.isGood) && (
            <>
              {renderSectionHeader('Good Ingredients')}
              {ingredientComparisons
                .filter(ingredient => ingredient.isGood)
                .map(renderIngredientRow)}
            </>
          )}

          {/* Short-term Impacts Section */}
          {impactComparisons.shortTermImpacts.length > 0 && (
            <>
              {renderSectionHeader('Short-term Impacts')}
              {impactComparisons.shortTermImpacts.map(renderImpactRow)}
            </>
          )}

          {/* Long-term Impacts Section */}
          {impactComparisons.longTermImpacts.length > 0 && (
            <>
              {renderSectionHeader('Long-term Impacts')}
              {impactComparisons.longTermImpacts.map(renderImpactRow)}
            </>
          )}

          {/* Allergens Section */}
          {selectedProducts.some(product => product.allergyWarnings.length > 0) && (
            <>
              {renderSectionHeader('Allergens')}
              {selectedProducts.map(product =>
                product.allergyWarnings.map((warning, index) => (
                  <View style={styles.tableRow} key={`${product.id}-allergen-${index}`}>
                    <View style={[styles.labelColumn, { width: columnWidth.labelColumnWidth }]}>
                      <Typography variant="bodyText" style={styles.allergenName}>
                        {warning.ingredient}
                      </Typography>
                    </View>

                    {selectedProducts.map(p => (
                      <View
                        key={p.id}
                        style={[
                          styles.productColumn,
                          { width: columnWidth.productColumnWidth }
                        ]}
                      >
                        {p.id === product.id ? (
                          <View style={styles.allergenExists}>
                            <Ionicons
                              name="warning"
                              size={16}
                              color={Colors.Warning}
                            />
                            <Typography
                              variant="description"
                              style={styles.allergenDescription}
                              numberOfLines={2}
                            >
                              {warning.message}
                            </Typography>
                          </View>
                        ) : (
                          <Typography variant="description" style={styles.notPresent}>
                            Not present
                          </Typography>
                        )}
                      </View>
                    ))}
                  </View>
                ))
              )}
            </>
          )}
        </ScrollView>
      </View>
    </Screen>
  );
};

const styles = StyleSheet.create({
  screen: {
    backgroundColor: Colors.BackgroundPrimary,
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: Spacing.Large,
    paddingTop: Spacing.Large,
    paddingBottom: Spacing.Medium,
  },
  title: {
    flex: 1,
    marginLeft: Spacing.Medium,
  },
  tableContainer: {
    flex: 1,
    paddingHorizontal: Spacing.Medium,
  },
  tableHeader: {
    flexDirection: 'row',
    borderBottomWidth: 1,
    borderBottomColor: Colors.SurfaceSecondary,
    paddingBottom: Spacing.Medium,
    backgroundColor: Colors.BackgroundPrimary,
    zIndex: 1,
  },
  sectionHeader: {
    paddingVertical: Spacing.Medium,
    borderBottomWidth: 1,
    borderBottomColor: Colors.SurfaceSecondary,
    backgroundColor: Colors.SurfaceSecondary,
    paddingHorizontal: Spacing.Medium,
    marginVertical: Spacing.Small,
  },
  tableRow: {
    flexDirection: 'row',
    borderBottomWidth: 1,
    borderBottomColor: Colors.SurfaceSecondary,
    paddingVertical: Spacing.Medium,
  },
  labelColumn: {
    paddingRight: Spacing.Small,
    justifyContent: 'center',
  },
  productColumn: {
    paddingHorizontal: Spacing.ExtraSmall,
    justifyContent: 'center',
    alignItems: 'center',
  },
  labelText: {
    fontWeight: '600',
  },
  scrollView: {
    flex: 1,
  },
  scrollContent: {
    paddingBottom: Spacing.XXLarge,
  },
  gradeContainer: {
    width: 40,
    height: 40,
    borderRadius: 20,
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: Spacing.Small,
  },
  gradeText: {
    fontWeight: '700',
  },
  productName: {
    textAlign: 'center',
    fontWeight: '600',
  },
  sectionTitle: {
    fontWeight: '600',
  },
  ingredientLabelContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  ingredientIcon: {
    marginRight: Spacing.Small,
  },
  ingredientName: {
    flex: 1,
  },
  goodIngredient: {
    color: Colors.Success,
  },
  badIngredient: {
    color: Colors.Error,
  },
  ingredientExists: {
    alignItems: 'center',
    justifyContent: 'center',
  },
  ingredientDescription: {
    textAlign: 'center',
    marginTop: Spacing.ExtraSmall,
    fontSize: 10,
  },
  notPresent: {
    color: Colors.LightText,
    fontSize: 10,
    textAlign: 'center',
  },
  impactText: {
    flex: 1,
  },
  impactIcon: {
    marginRight: Spacing.Small,
  },
  allergenName: {
    color: Colors.Warning,
  },
  allergenExists: {
    alignItems: 'center',
    justifyContent: 'center',
  },
  allergenDescription: {
    textAlign: 'center',
    marginTop: Spacing.ExtraSmall,
    fontSize: 10,
  },
});

export default ComparisonViewScreen;