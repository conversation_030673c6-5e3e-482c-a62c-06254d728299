import React from 'react';
import { View, StyleSheet, ScrollView, Text, Alert } from 'react-native';
import { useNavigation } from '@react-navigation/native';
import { NativeStackNavigationProp } from '@react-navigation/native-stack';
import { Colors, Spacing } from '../theme';
import Screen from '../Components/Screen';
import Typography from '../Components/Typography';
import BackButton from '../Components/BackButton';
import ListRow from '../Components/ListRow';
import { Ionicons } from '@expo/vector-icons';

type RootStackParamList = {
  Settings: undefined;
  SettingsAbout: undefined;
};

type SettingsAboutScreenNavigationProp = NativeStackNavigationProp<
  RootStackParamList,
  'SettingsAbout'
>;

// Legal links
const legalLinks = [
  {
    id: 'privacy',
    title: 'Privacy Policy',
    icon: <Ionicons name="shield-outline" size={24} color={Colors.DarkText} />,
    action: () => console.log('Open Privacy Policy'),
  },
  {
    id: 'terms',
    title: 'Terms of Service',
    icon: <Ionicons name="document-text-outline" size={24} color={Colors.DarkText} />,
    action: () => console.log('Open Terms of Service'),
  },
  {
    id: 'licenses',
    title: 'Open Source Licenses',
    icon: <Ionicons name="code-slash-outline" size={24} color={Colors.DarkText} />,
    action: () => console.log('Open Licenses'),
  },
  {
    id: 'acknowledgements',
    title: 'Acknowledgements',
    icon: <Ionicons name="heart-outline" size={24} color={Colors.DarkText} />,
    action: () => console.log('Open Acknowledgements'),
  },
];

const SettingsAboutScreen: React.FC = () => {
  const navigation = useNavigation<SettingsAboutScreenNavigationProp>();

  const handleBack = () => {
    navigation.navigate('Settings');
  };

  const handleLinkPress = (link: typeof legalLinks[0]) => {
    // For this mock implementation, just show an alert
    Alert.alert(
      link.title,
      `You selected ${link.title}. This would open the ${link.title} document in a real app.`,
      [{ text: 'OK', onPress: link.action }]
    );
  };

  return (
    <Screen style={styles.container}>
      <View style={styles.header}>
        <BackButton onPress={handleBack} />
        <Typography variant="heading1" style={styles.title}>
          About
        </Typography>
      </View>

      <ScrollView style={styles.content} contentContainerStyle={styles.scrollContent}>
        {/* App Information */}
        <View style={styles.appInfoContainer}>
          <View style={styles.logoContainer}>
            <Text style={styles.logoText}>JC</Text>
          </View>
          <Typography variant="heading1" style={styles.appName}>
            JunkChk
          </Typography>
          <Typography variant="bodyText" style={styles.appDescription}>
            Your smart health companion for food and cosmetics
          </Typography>
          <Typography variant="description" style={styles.appVersion}>
            Version 1.0.0 (Build 123)
          </Typography>
        </View>

        {/* Legal Links */}
        <Typography variant="heading2" style={styles.sectionTitle}>
          Legal Information
        </Typography>
        <View style={styles.linksContainer}>
          {legalLinks.map((link) => (
            <ListRow
              key={link.id}
              title={link.title}
              leftIcon={link.icon}
              rightIcon={<Ionicons name="chevron-forward" size={20} color={Colors.LightText} />}
              onPress={() => handleLinkPress(link)}
            />
          ))}
        </View>

        {/* Copyright */}
        <View style={styles.copyrightContainer}>
          <Typography variant="caption" style={styles.copyrightText}>
            © 2023 JunkChk. All rights reserved.
          </Typography>
        </View>
      </ScrollView>
    </Screen>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.BackgroundPrimary,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: Spacing.Large,
    paddingTop: Spacing.Large,
    paddingBottom: Spacing.Medium,
  },
  title: {
    marginLeft: Spacing.Medium,
  },
  content: {
    flex: 1,
  },
  scrollContent: {
    paddingBottom: Spacing.XXLarge,
  },
  appInfoContainer: {
    alignItems: 'center',
    paddingVertical: Spacing.Large,
    paddingHorizontal: Spacing.Large,
  },
  logoContainer: {
    width: 100,
    height: 100,
    borderRadius: 50,
    backgroundColor: Colors.DarkText,
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: Spacing.Medium,
  },
  logoText: {
    fontSize: 40,
    fontWeight: 'bold',
    color: Colors.BackgroundPrimary,
  },
  appName: {
    marginBottom: Spacing.Small,
  },
  appDescription: {
    textAlign: 'center',
    marginBottom: Spacing.Medium,
    paddingHorizontal: Spacing.Large,
  },
  appVersion: {
    color: Colors.LightText,
  },
  sectionTitle: {
    paddingHorizontal: Spacing.Large,
    marginTop: Spacing.Large,
    marginBottom: Spacing.Medium,
  },
  linksContainer: {
    marginBottom: Spacing.Large,
  },
  copyrightContainer: {
    alignItems: 'center',
    paddingVertical: Spacing.Large,
  },
  copyrightText: {
    color: Colors.LightText,
  },
});

export default SettingsAboutScreen;
