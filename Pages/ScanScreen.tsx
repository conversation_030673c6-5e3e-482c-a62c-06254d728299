import React, { useState, useRef, useEffect } from 'react';
import { View, StyleSheet, TouchableOpacity, Text, Alert, SafeAreaView, Platform } from 'react-native';
import { useNavigation, useRoute, RouteProp } from '@react-navigation/native';
import { NativeStackNavigationProp } from '@react-navigation/native-stack';
import { useAuth } from '../context';

import { Colors, Spacing, Shadow, BorderRadius } from '../theme';
import { Ionicons } from '@expo/vector-icons';
import { Camera, CameraView, CameraType } from 'expo-camera';
import * as ImagePicker from 'expo-image-picker';
import * as FileSystem from 'expo-file-system';
import * as ImageManipulator from 'expo-image-manipulator';
import BackButton from '../Components/BackButton';
import Typography from '../Components/Typography';
import Screen from '../Components/Screen';
import GlowingCaptureButton from '../Components/GlowingCaptureButton';
import GlowingElement from '../Components/GlowingElement';
import { RootStackParamList } from '../navigation/types';
import { testNetworkConnectivity } from '../src/utils/networkTest';

type ScanScreenNavigationProp = NativeStackNavigationProp<RootStackParamList, 'Scan'>;
type ScanScreenRouteProp = RouteProp<RootStackParamList, 'Scan'>;


const ScanScreen: React.FC = () => {
  const navigation = useNavigation<ScanScreenNavigationProp>();
  const route = useRoute<ScanScreenRouteProp>();
  const { authState } = useAuth();

  const [fromMainNav, setFromMainNav] = useState(route.params?.fromMainNav || false);
  const [hasPermission, setHasPermission] = useState<boolean | null>(null);
  const [cameraType, setCameraType] = useState<CameraType>('back');
  const [flashMode, setFlashMode] = useState<'off' | 'on' | 'auto'>('off');
  const cameraRef = useRef<CameraView | null>(null);

  useEffect(() => {
    // Request camera permissions
    (async () => {
      try {
        const { status } = await Camera.requestCameraPermissionsAsync();
        setHasPermission(status === 'granted');
        if (status !== 'granted') {
          console.log('Camera permission denied');
          Alert.alert(
            'Camera Permission Required',
            'Please grant camera permission to use the scanner feature.',
            [{ text: 'OK' }]
          );
        }
      } catch (error) {
        console.error('Error requesting camera permission:', error);
        setHasPermission(false);
        Alert.alert(
          'Camera Error',
          'There was an error accessing your camera. Please try again.',
          [{ text: 'OK' }]
        );
      }
    })();
  }, []);

  // Image processing function with improved error handling
  const resizeImage = async (uri: string) => {
    try {
      // Verify the source image exists before attempting to manipulate
      const fileInfo = await FileSystem.getInfoAsync(uri);
      if (!fileInfo.exists) {
        throw new Error('Source image file does not exist');
      }

      // Set a reasonable timeout for image manipulation
      const manipulationPromise = ImageManipulator.manipulateAsync(
        uri,
        [{ resize: { width: 1024 } }],
        { compress: 0.9, format: ImageManipulator.SaveFormat.JPEG } // Slightly reduce quality for better performance
      );

      // Set a timeout to prevent hanging (increased to 30 seconds for better reliability)
      const timeoutPromise = new Promise((_, reject) =>
        setTimeout(() => reject(new Error('Image manipulation timed out')), 30000)
      );

      // Race the manipulation against the timeout
      const manipResult = await Promise.race([manipulationPromise, timeoutPromise]) as ImageManipulator.ImageResult;

      if (!manipResult || !manipResult.uri) {
        throw new Error('Image manipulation failed - no result returned');
      }

      return manipResult.uri;
    } catch (error) {
      console.error('Error resizing image:', error);
      // Return the original URI if manipulation fails
      return uri;
    }
  };

  const handleCapture = async () => {
    if (!cameraRef.current) {
      console.error('Camera reference is not available');
      Alert.alert('Camera Error', 'Camera is not ready. Please try again.');
      return;
    }

    try {
      // Add a small delay to ensure camera is fully initialized
      await new Promise(resolve => setTimeout(resolve, 100));

      const photo = await cameraRef.current.takePictureAsync();

      if (!photo || !photo.uri) {
        throw new Error('Photo capture failed - no photo data returned');
      }

      console.log('ScanScreen: capturing photo, isAuthenticated=', authState.isAuthenticated);

      // Verify the photo URI exists
      const fileInfo = await FileSystem.getInfoAsync(photo.uri);
      if (!fileInfo.exists) {
        throw new Error('Photo file does not exist after capture');
      }

      navigation.navigate('Processing', {
        photoUri: photo.uri,
        fromMainNav,
        isAuthenticated: authState.isAuthenticated
      });
    } catch (error) {
      console.error('Error taking picture:', error);
      Alert.alert(
        'Camera Error',
        'Failed to take picture. Please try again or use the gallery option instead.',
        [{ text: 'OK' }]
      );
    }
  };

  const handleGallerySelection = async () => {
    try {
      console.log('Gallery button pressed - starting image selection');

      // Request media library permissions if not already granted
      const { status } = await ImagePicker.requestMediaLibraryPermissionsAsync();

      if (status !== 'granted') {
        Alert.alert(
          'Permission Denied',
          'Sorry, we need media library permissions to access your photos.',
          [{ text: 'OK' }]
        );
        return;
      }

      // Launch image picker with a timeout to prevent hanging
      const pickerPromise = ImagePicker.launchImageLibraryAsync({
        mediaTypes: ImagePicker.MediaTypeOptions.Images,
        allowsEditing: true,
        aspect: [4, 3],
        quality: 1,
      });

      // Set a timeout for the picker (increased for better reliability)
      const timeoutPromise = new Promise((_, reject) =>
        setTimeout(() => reject(new Error('Image picker timed out')), 60000)
      );

      // Race the picker against the timeout
      const result = await Promise.race([pickerPromise, timeoutPromise]) as ImagePicker.ImagePickerResult;

      if (!result.canceled && result.assets && result.assets.length > 0) {
        if (!result.assets[0].uri) {
          throw new Error('No image URI returned from picker');
        }

        // Verify the image file exists
        const fileInfo = await FileSystem.getInfoAsync(result.assets[0].uri);
        if (!fileInfo.exists) {
          throw new Error('Selected image file does not exist');
        }

        console.log('Image selected successfully, navigating to processing screen');

        // Navigate immediately to processing screen for better UX
        // Image processing will happen in ProcessingScreen
        navigation.navigate('Processing', {
          photoUri: result.assets[0].uri,
          fromMainNav,
          isAuthenticated: authState.isAuthenticated
        });
      }
    } catch (error) {
      console.error('Error selecting image:', error);
      Alert.alert(
        'Gallery Error',
        'Failed to select image. Please try again or use the camera instead.',
        [{ text: 'OK' }]
      );
    }
  };

  const toggleFlash = () => {
    setFlashMode(
      flashMode === 'off'
        ? 'on'
        : 'off'
    );
  };

  const toggleCameraType = () => {
    setCameraType(
      cameraType === 'back'
        ? 'front'
        : 'back'
    );
  };

  const handleBack = () => {
    navigation.goBack();
  };

  // Test network connectivity (for debugging)
  const testConnectivity = async () => {
    console.log('Testing network connectivity...');
    const result = await testNetworkConnectivity();

    if (result.success) {
      Alert.alert(
        'Network Test Successful',
        `Server is reachable at ${result.serverUrl}`,
        [{ text: 'OK' }]
      );
    } else {
      Alert.alert(
        'Network Test Failed',
        `Cannot reach server at ${result.serverUrl}\nError: ${result.error}`,
        [{ text: 'OK' }]
      );
    }
  };

  // Handle permissions
  if (hasPermission === null) {
    return (
      <View style={styles.container}>
        <Typography variant="heading2" color={Colors.BackgroundPrimary} style={styles.permissionText}>
          Requesting camera permission...
        </Typography>
      </View>
    );
  }

  if (hasPermission === false) {
    return (
      <View style={styles.container}>
        <Typography variant="heading2" color={Colors.BackgroundPrimary} style={styles.permissionText}>
          No access to camera. Please enable camera permissions in your device settings.
        </Typography>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <CameraView
        ref={cameraRef}
        style={styles.camera}
        facing={cameraType}
        flash={flashMode}
      >
        <View style={styles.overlay}>
          {/* Header with back button */}
          {fromMainNav && (
            <View style={styles.header}>
              <BackButton onPress={handleBack} color={Colors.BackgroundPrimary} />
            </View>
          )}

          {/* Title at the top */}
          <View style={styles.titleContainer}>
            <Typography
              variant="heading2"
              color={Colors.BackgroundPrimary}
              style={styles.titleText}
            >
              Scan Product Label
            </Typography>

            {/* Debug button for development */}
            {__DEV__ && (
              <TouchableOpacity
                style={styles.debugButton}
                onPress={testConnectivity}
                activeOpacity={0.7}
              >
                <Text style={styles.debugButtonText}>Test Server</Text>
              </TouchableOpacity>
            )}
          </View>
        </View>
      </CameraView>

      {/* Fixed controls at the bottom */}
      <View style={[styles.fixedControlsContainer, Shadow.Large]}>
        <View style={styles.controlsRow}>
          {/* Flash toggle button */}
          <GlowingElement
            color={Colors.BackgroundPrimary}
            intensity={0.5}
            active={true}
            size={50}
          >
            <TouchableOpacity
              style={[styles.controlButton, Shadow.Medium]}
              onPress={toggleFlash}
              activeOpacity={0.7}
            >
              <Ionicons
                name={flashMode === 'off' ? "flash-off" : "flash"}
                size={24}
                color={Colors.BackgroundPrimary}
              />
            </TouchableOpacity>
          </GlowingElement>

          {/* Capture button (center) */}
          <GlowingCaptureButton
            onPress={handleCapture}
            size={90}
            iconSize={40}
            glowIntensity={1.0}
            style={styles.captureButton}
          />

          {/* Gallery selection button */}
          <GlowingElement
            color={Colors.BackgroundPrimary}
            intensity={0.5}
            active={true}
            size={50}
          >
            <TouchableOpacity
              style={[styles.controlButton, Shadow.Medium]}
              onPress={handleGallerySelection}
              activeOpacity={0.7}
            >
              <Ionicons
                name="images"
                size={24}
                color={Colors.BackgroundPrimary}
              />
            </TouchableOpacity>
          </GlowingElement>

          {/* Camera type toggle button */}
          <GlowingElement
            color={Colors.BackgroundPrimary}
            intensity={0.5}
            active={true}
            size={50}
          >
            <TouchableOpacity
              style={[styles.controlButton, Shadow.Medium]}
              onPress={toggleCameraType}
              activeOpacity={0.7}
            >
              <Ionicons
                name="camera-reverse"
                size={24}
                color={Colors.BackgroundPrimary}
              />
            </TouchableOpacity>
          </GlowingElement>
        </View>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.DarkText,
  },
  camera: {
    flex: 1,
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
  },
  overlay: {
    flex: 1,
    backgroundColor: 'transparent',
  },
  header: {
    padding: Spacing.Medium,
    paddingTop: Platform.OS === 'ios' ? Spacing.XXLarge : Spacing.Large,
    position: 'absolute',
    top: 0,
    left: 0,
    zIndex: 10,
  },
  titleContainer: {
    position: 'absolute',
    top: Platform.OS === 'ios' ? 60 : 40,
    width: '100%',
    alignItems: 'center',
    paddingHorizontal: Spacing.Medium,
    zIndex: 5,
  },
  titleText: {
    textAlign: 'center',
    textShadowColor: 'rgba(0, 0, 0, 0.75)',
    textShadowOffset: { width: 0, height: 1 },
    textShadowRadius: 5,
    fontWeight: 'bold',
  },
  // Fixed controls container at the bottom of the screen
  fixedControlsContainer: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    paddingBottom: Platform.OS === 'ios' ? Spacing.XXLarge : Spacing.Large,
    paddingTop: Spacing.Medium,
    borderTopLeftRadius: BorderRadius.Large,
    borderTopRightRadius: BorderRadius.Large,
    ...Platform.select({
      ios: {
        paddingBottom: 34, // Extra padding for iOS devices with home indicator
      },
    }),
  },
  controlsRow: {
    flexDirection: 'row',
    justifyContent: 'space-evenly',
    alignItems: 'center',
    paddingHorizontal: Spacing.Medium,
    paddingVertical: Spacing.Medium,
  },
  controlButton: {
    width: 50,
    height: 50,
    borderRadius: 25,
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: 'rgba(0, 0, 0, 0.6)',
    borderWidth: 1.5,
    borderColor: 'rgba(255, 255, 255, 0.5)',
  },
  permissionText: {
    textAlign: 'center',
    marginHorizontal: Spacing.Large,
    color: Colors.BackgroundPrimary,
  },
  captureButton: {
    marginHorizontal: Spacing.Medium,
  },
  debugButton: {
    marginTop: Spacing.Medium,
    paddingHorizontal: Spacing.Medium,
    paddingVertical: Spacing.Small,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    borderRadius: BorderRadius.Medium,
    borderWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.3)',
  },
  debugButtonText: {
    color: Colors.BackgroundPrimary,
    fontSize: 12,
    fontWeight: 'bold',
    textAlign: 'center',
  },
});

export default ScanScreen;
