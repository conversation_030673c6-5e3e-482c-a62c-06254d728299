import React from 'react';
import { View, StyleSheet, Image } from 'react-native';
import { useNavigation } from '@react-navigation/native';
import { NativeStackNavigationProp } from '@react-navigation/native-stack';
import { Colors, Spacing } from '../theme';
import Screen from '../Components/Screen';
import Typography from '../Components/Typography';
import CustomButton from '../Components/CustomButton';
import GlowingButton from '../Components/GlowingButton';
import { Ionicons } from '@expo/vector-icons';

type RootStackParamList = {
  HowJunkChkWorks: undefined;
  Scan: undefined;
};

type HowJunkChkWorksScreenNavigationProp = NativeStackNavigationProp<
  RootStackParamList,
  'HowJunkChkWorks'
>;

const HowJunkChkWorksScreen: React.FC = () => {
  const navigation = useNavigation<HowJunkChkWorksScreenNavigationProp>();

  const handleNext = () => {
    navigation.navigate('Scan');
  };

  return (
    <Screen style={styles.container}>
      <View style={styles.content}>
        <Typography variant="heading1" style={styles.title}>
          How it Works
        </Typography>

        <View style={styles.stepsContainer}>
          <View style={styles.stepItem}>
            <View style={styles.iconContainer}>
              <Ionicons name="camera" size={28} color={Colors.BackgroundPrimary} />
            </View>
            <View style={styles.stepTextContainer}>
              <Typography variant="bodyText" style={styles.stepTitle}>
                Take a Photo
              </Typography>
              <Typography variant="description" style={styles.stepDescription}>
                Snap a picture of the product's ingredient list
              </Typography>
            </View>
          </View>

          <View style={styles.stepItem}>
            <View style={styles.iconContainer}>
              <Ionicons name="search" size={28} color={Colors.BackgroundPrimary} />
            </View>
            <View style={styles.stepTextContainer}>
              <Typography variant="bodyText" style={styles.stepTitle}>
                Analyze
              </Typography>
              <Typography variant="description" style={styles.stepDescription}>
                Our AI analyzes the ingredients for safety and health impact
              </Typography>
            </View>
          </View>

          <View style={styles.stepItem}>
            <View style={styles.iconContainer}>
              <Ionicons name="ribbon" size={28} color={Colors.BackgroundPrimary} />
            </View>
            <View style={styles.stepTextContainer}>
              <Typography variant="bodyText" style={styles.stepTitle}>
                Get a Grade
              </Typography>
              <Typography variant="description" style={styles.stepDescription}>
                See an overall grade from A to E based on ingredient quality
              </Typography>
            </View>
          </View>

          <View style={styles.stepItem}>
            <View style={styles.iconContainer}>
              <Ionicons name="list" size={28} color={Colors.BackgroundPrimary} />
            </View>
            <View style={styles.stepTextContainer}>
              <Typography variant="bodyText" style={styles.stepTitle}>
                See Details
              </Typography>
              <Typography variant="description" style={styles.stepDescription}>
                Review good and bad ingredients with personalized highlights
              </Typography>
            </View>
          </View>
        </View>
      </View>
      <View style={styles.footer}>
        <GlowingButton
          title="Got It, Let's Scan"
          onPress={handleNext}
          variant="primary"
          style={styles.button}
          glowIntensity={0.7}
          alwaysGlow={true}
          glowColor={Colors.Success}
        />
      </View>
    </Screen>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.BackgroundPrimary,
  },
  content: {
    flex: 1,
    paddingHorizontal: Spacing.Large,
    paddingTop: Spacing.ExtraLarge,
  },
  title: {
    marginBottom: Spacing.ExtraLarge,
    textAlign: 'center',
  },
  stepsContainer: {
    marginTop: Spacing.Large,
  },
  stepItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: Spacing.Large,
  },
  iconContainer: {
    width: 48,
    height: 48,
    borderRadius: 24,
    backgroundColor: Colors.DarkText,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: Spacing.Medium,
  },
  stepTextContainer: {
    flex: 1,
  },
  stepTitle: {
    fontWeight: '600',
    marginBottom: Spacing.ExtraSmall,
  },
  stepDescription: {
    lineHeight: 20,
  },
  footer: {
    paddingHorizontal: Spacing.Large,
    paddingBottom: Spacing.ExtraLarge,
  },
  button: {
    width: '100%',
  },
});

export default HowJunkChkWorksScreen;
