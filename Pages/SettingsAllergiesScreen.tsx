import React, { useState, useEffect } from 'react';
import { View, StyleSheet, ScrollView } from 'react-native';
import { useNavigation } from '@react-navigation/native';
import { NativeStackNavigationProp } from '@react-navigation/native-stack';
import { Colors, Spacing } from '../theme';
import Screen from '../Components/Screen';
import Typography from '../Components/Typography';
import BackButton from '../Components/BackButton';
import Tag from '../Components/Tag';
import Input from '../Components/Input';
import { usePersonalization } from '../context';
import { Ionicons } from '@expo/vector-icons';

type RootStackParamList = {
  Settings: undefined;
  SettingsAllergies: undefined;
};

type SettingsAllergiesScreenNavigationProp = NativeStackNavigationProp<
  RootStackParamList,
  'SettingsAllergies'
>;

const commonAllergies = [
  'Gluten',
  'Dairy',
  'Peanuts',
  'Tree Nuts',
  'Soy',
  'Shellfish',
  'Eggs',
  'Fish',
  'Vegan',
  'Vegetarian',
];

const SettingsAllergiesScreen: React.FC = () => {
  const { personalizationState, setAllergiesRestrictions } = usePersonalization();
  const navigation = useNavigation<SettingsAllergiesScreenNavigationProp>();
    const [selectedAllergies, setSelectedAllergies] = useState<string[]>(personalizationState.allergiesRestrictions);
    const [customAllergy, setCustomAllergy] = useState('');

  // Sync local state when personalization state changes (e.g., when data finishes loading)
  useEffect(() => {
    setSelectedAllergies(personalizationState.allergiesRestrictions);
  }, [personalizationState.allergiesRestrictions]);

  const handleAllergyToggle = (allergy: string) => {
        if (selectedAllergies.includes(allergy)) {
      const updated = selectedAllergies.filter((a) => a !== allergy);
      setSelectedAllergies(updated);
      setAllergiesRestrictions(updated);
    } else {
      const updated = [...selectedAllergies, allergy];
      setSelectedAllergies(updated);
      setAllergiesRestrictions(updated);
    }
  };

  const handleAddCustomAllergy = () => {
        if (customAllergy.trim() && !selectedAllergies.includes(customAllergy.trim())) {
      const updated = [...selectedAllergies, customAllergy.trim()];
      setSelectedAllergies(updated);
      setAllergiesRestrictions(updated);
      setCustomAllergy('');
    }
  };

  const handleBack = () => {
    navigation.navigate('Settings');
  };

  return (
    <Screen style={styles.container}>
      <View style={styles.header}>
        <BackButton onPress={handleBack} />
        <Typography variant="heading1" style={styles.title}>
          Allergies & Restrictions
        </Typography>
      </View>

      <ScrollView style={styles.content} contentContainerStyle={styles.scrollContent}>
        <Typography variant="bodyText" style={styles.description}>
          Select allergies and dietary restrictions to highlight in your scan results.
          These will be used to warn you when a product contains ingredients you should avoid.
        </Typography>

        <Typography variant="bodyText" style={styles.sectionTitle}>
          Common Allergies & Restrictions
        </Typography>
        <View style={styles.allergiesContainer}>
          {commonAllergies.map((allergy, index) => (
            <Tag
              key={index}
              label={allergy}
              selected={selectedAllergies.includes(allergy)}
              onPress={() => handleAllergyToggle(allergy)}
            />
          ))}
        </View>

        <Typography variant="bodyText" style={styles.sectionTitle}>
          Add Custom Allergy or Restriction
        </Typography>
        <View style={styles.customAllergyContainer}>
          <Input
            placeholder="Enter allergy or restriction"
            value={customAllergy}
            onChangeText={setCustomAllergy}
            returnKeyType="done"
            onSubmitEditing={handleAddCustomAllergy}
            rightIcon={
              <Ionicons
                name="add-circle"
                size={24}
                color={Colors.DarkText}
                onPress={handleAddCustomAllergy}
              />
            }
          />
        </View>

        {selectedAllergies.length > 0 && (
          <View style={styles.selectedAllergiesContainer}>
            <Typography variant="bodyText" style={styles.sectionTitle}>
              Your Selected Allergies & Restrictions
            </Typography>
            <View style={styles.selectedAllergiesTags}>
              {selectedAllergies.map((allergy, index) => (
                <Tag
                  key={index}
                  label={allergy}
                  selected
                  onRemove={() => handleAllergyToggle(allergy)}
                />
              ))}
            </View>
          </View>
        )}
      </ScrollView>
    </Screen>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.BackgroundPrimary,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: Spacing.Large,
    paddingTop: Spacing.Large,
    paddingBottom: Spacing.Medium,
  },
  title: {
    marginLeft: Spacing.Medium,
  },
  content: {
    flex: 1,
  },
  scrollContent: {
    paddingHorizontal: Spacing.Large,
    paddingBottom: Spacing.XXLarge,
  },
  description: {
    marginBottom: Spacing.Large,
  },
  sectionTitle: {
    fontWeight: '600',
    marginBottom: Spacing.Medium,
  },
  allergiesContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    marginBottom: Spacing.Large,
  },
  customAllergyContainer: {
    marginBottom: Spacing.Large,
  },
  selectedAllergiesContainer: {
    marginBottom: Spacing.Large,
  },
  selectedAllergiesTags: {
    flexDirection: 'row',
    flexWrap: 'wrap',
  },
});

export default SettingsAllergiesScreen;
