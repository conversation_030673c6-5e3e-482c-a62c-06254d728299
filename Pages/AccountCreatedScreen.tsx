import React from 'react';
import { View, StyleSheet, BackHandler } from 'react-native';
import { useNavigation, useRoute, RouteProp, useFocusEffect } from '@react-navigation/native';
import { NativeStackNavigationProp } from '@react-navigation/native-stack';
import { useScan, useHistory, ScanResult, Grade } from '../context';
import { Colors, Spacing } from '../theme';
import Screen from '../Components/Screen';
import Typography from '../Components/Typography';
import CustomButton from '../Components/CustomButton';
import StatusIndicator from '../Components/StatusIndicator';
import { Ionicons } from '@expo/vector-icons';

type RootStackParamList = {
  AccountCreated: {
    returnToScan?: boolean;
    scanData?: any;
  };
  History: undefined;
  ScanResults: {
    imageUri: string;
    isAnonymous: boolean;
    apiResponse: any;
  };
};

type AccountCreatedScreenNavigationProp = NativeStackNavigationProp<
  RootStackParamList,
  'AccountCreated'
>;

// Generate a unique ID for scan results
const generateUniqueId = () => {
  return `scan-${Date.now()}-${Math.random().toString(36).substring(2, 9)}`;
};

const AccountCreatedScreen: React.FC = () => {
  const navigation = useNavigation<AccountCreatedScreenNavigationProp>();
  const route = useRoute<RouteProp<RootStackParamList, 'AccountCreated'>>();
  const { completeScan } = useScan();
  const { addScanToHistory } = useHistory();

  // Prevent hardware back button on Android
  useFocusEffect(
    React.useCallback(() => {
      const onBackPress = () => {
        // Return true to prevent default behavior (going back)
        return true;
      };

      // Add event listener
      const subscription = BackHandler.addEventListener('hardwareBackPress', onBackPress);

      // Clean up event listener on unmount
      return () => subscription.remove();
    }, [])
  );

  const handleContinue = async () => {
    // Get parameters from route
    const { returnToScan, scanData } = route.params || {};

    // Log for debugging
    console.log('AccountCreated params:', { returnToScan, scanData });

    // Always navigate to Home after account creation
    // This is the main entry point to the app after authentication
    navigation.navigate('Home');
  };

  return (
    <Screen style={styles.container}>
      <View style={styles.content}>
        <StatusIndicator
          type="success"
          size="large"
          icon={<Ionicons name="checkmark" size={40} color={Colors.BackgroundPrimary} />}
          style={styles.statusIndicator}
        />
        <Typography variant="heading1" style={styles.title}>
          Account created
        </Typography>
        <Typography variant="bodyText" style={styles.description}>
          Your account has been successfully created, and your preferences saved.
        </Typography>
      </View>
      <View style={styles.footer}>
        <CustomButton
          title="Continue"
          onPress={handleContinue}
          variant="primary"
          style={styles.button}
        />
      </View>
    </Screen>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.BackgroundPrimary,
  },
  content: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    paddingHorizontal: Spacing.Large,
  },
  statusIndicator: {
    marginBottom: Spacing.Large,
  },
  title: {
    marginBottom: Spacing.Medium,
    textAlign: 'center',
  },
  description: {
    textAlign: 'center',
    marginBottom: Spacing.ExtraLarge,
  },
  footer: {
    paddingHorizontal: Spacing.Large,
    paddingBottom: Spacing.ExtraLarge,
  },
  button: {
    width: '100%',
  },
});

export default AccountCreatedScreen;
