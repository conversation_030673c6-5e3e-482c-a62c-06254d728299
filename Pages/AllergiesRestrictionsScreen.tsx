import React, { useState, useEffect } from 'react';
import { View, StyleSheet, ScrollView } from 'react-native';
import { useNavigation } from '@react-navigation/native';
import { NativeStackNavigationProp } from '@react-navigation/native-stack';
import { Colors, Spacing } from '../theme';
import Screen from '../Components/Screen';
import Animated, {
  useSharedValue,
  useAnimatedStyle,
  withTiming,
  withDelay,
  withSequence,
  withRepeat,
  Easing,
  interpolate,
} from 'react-native-reanimated';
import { AnimatedText, AnimatedView, AnimatedButton } from '../Components/AnimatedComponents';
import { AnimationDuration, AnimationEasing } from '../Components/animations';
import AnimatedPersonalizationHeader from '../Components/AnimatedPersonalizationHeader';
import Tag from '../Components/Tag';
import Input from '../Components/Input';
import { Ionicons } from '@expo/vector-icons';
import { usePersonalization } from '../context';

type RootStackParamList = {
  AllergiesRestrictions: undefined;
  YourGoals: undefined;
  HowYourAnswersHelp: undefined;
};

type AllergiesRestrictionsScreenNavigationProp = NativeStackNavigationProp<
  RootStackParamList,
  'AllergiesRestrictions'
>;

const commonAllergies = [
  'Gluten',
  'Dairy',
  'Peanuts',
  'Tree Nuts',
  'Soy',
  'Shellfish',
  'Eggs',
  'Fish',
  'Vegan',
  'Vegetarian',
];

const AllergiesRestrictionsScreen: React.FC = () => {
  const navigation = useNavigation<AllergiesRestrictionsScreenNavigationProp>();
  const { personalizationState, setAllergiesRestrictions } = usePersonalization();
  const [selectedAllergies, setSelectedAllergies] = useState<string[]>(personalizationState.allergiesRestrictions);
  const [customAllergy, setCustomAllergy] = useState('');

  // Sync local state when personalization state changes (e.g., when data is reset)
  useEffect(() => {
    setSelectedAllergies(personalizationState.allergiesRestrictions);
  }, [personalizationState.allergiesRestrictions]);

  // Animation values
  const contentOpacity = useSharedValue(0);
  const allergyItemAnimations = commonAllergies.map(() => useSharedValue(0));
  const inputOpacity = useSharedValue(0);
  const selectedOpacity = useSharedValue(0);
  const buttonOpacity = useSharedValue(0);

  // Background animation values
  const backgroundAnim = useSharedValue(0);

  useEffect(() => {
    // Animate content
    contentOpacity.value = withDelay(
      300,
      withTiming(1, {
        duration: AnimationDuration.Medium,
        easing: AnimationEasing.Smooth
      })
    );

    // Animate allergy items sequentially
    allergyItemAnimations.forEach((anim, index) => {
      anim.value = withDelay(
        500 + (index * 50),
        withTiming(1, {
          duration: AnimationDuration.Medium,
          easing: AnimationEasing.Smooth
        })
      );
    });

    // Animate input field
    inputOpacity.value = withDelay(
      500 + (commonAllergies.length * 50) + 100,
      withTiming(1, {
        duration: AnimationDuration.Medium,
        easing: AnimationEasing.Smooth
      })
    );

    // Animate button
    buttonOpacity.value = withDelay(
      500 + (commonAllergies.length * 50) + 200,
      withTiming(1, {
        duration: AnimationDuration.Medium,
        easing: AnimationEasing.Smooth
      })
    );

    // Start background animation
    backgroundAnim.value = withRepeat(
      withSequence(
        withTiming(1, { duration: 15000, easing: Easing.inOut(Easing.ease) }),
        withTiming(0, { duration: 15000, easing: Easing.inOut(Easing.ease) })
      ),
      -1, // Infinite repeat
      true // Reverse
    );

    return () => {
      // Cleanup
      contentOpacity.value = 0;
      allergyItemAnimations.forEach(anim => { anim.value = 0; });
      inputOpacity.value = 0;
      selectedOpacity.value = 0;
      buttonOpacity.value = 0;
      backgroundAnim.value = 0;
    };
  }, []);

  // Update selected allergies animation when they change
  useEffect(() => {
    if (selectedAllergies.length > 0) {
      selectedOpacity.value = withTiming(1, {
        duration: AnimationDuration.Medium,
        easing: AnimationEasing.Smooth
      });
    } else {
      selectedOpacity.value = withTiming(0, {
        duration: AnimationDuration.Short,
        easing: AnimationEasing.Smooth
      });
    }
  }, [selectedAllergies]);

  // Animated styles
  const contentAnimatedStyle = useAnimatedStyle(() => ({
    opacity: contentOpacity.value,
    transform: [
      { translateY: interpolate(contentOpacity.value, [0, 1], [20, 0]) }
    ]
  }));

  const inputAnimatedStyle = useAnimatedStyle(() => ({
    opacity: inputOpacity.value,
    transform: [
      { translateY: interpolate(inputOpacity.value, [0, 1], [10, 0]) }
    ]
  }));

  const selectedAnimatedStyle = useAnimatedStyle(() => ({
    opacity: selectedOpacity.value,
    transform: [
      { translateY: interpolate(selectedOpacity.value, [0, 1], [10, 0]) }
    ]
  }));

  const buttonAnimatedStyle = useAnimatedStyle(() => ({
    opacity: buttonOpacity.value,
    transform: [
      { scale: interpolate(buttonOpacity.value, [0, 1], [0.95, 1]) }
    ]
  }));

  const backgroundAnimatedStyle = useAnimatedStyle(() => {
    return {
      opacity: 0.05,
      transform: [
        { scale: 1 + (backgroundAnim.value * 0.1) },
        { rotate: `${backgroundAnim.value * 10}deg` },
      ],
    };
  });

  const handleAllergyToggle = (allergy: string) => {
    let newAllergies: string[];
    if (selectedAllergies.includes(allergy)) {
      newAllergies = selectedAllergies.filter((a) => a !== allergy);
    } else {
      newAllergies = [...selectedAllergies, allergy];
    }
    setSelectedAllergies(newAllergies);
    setAllergiesRestrictions(newAllergies);
  };

  const handleAddCustomAllergy = () => {
    if (customAllergy.trim() && !selectedAllergies.includes(customAllergy.trim())) {
      const newAllergies = [...selectedAllergies, customAllergy.trim()];
      setSelectedAllergies(newAllergies);
      setAllergiesRestrictions(newAllergies);
      setCustomAllergy('');
    }
  };

  const handleBack = () => {
    navigation.navigate('YourGoals');
  };

  const handleNext = async () => {
    await setAllergiesRestrictions(selectedAllergies);
    navigation.navigate('HowYourAnswersHelp');
  };

  return (
    <View style={styles.container}>
      {/* Animated background element */}
      <Animated.View style={[styles.backgroundElement, backgroundAnimatedStyle]} />

      <AnimatedPersonalizationHeader
        currentStep={2}
        totalSteps={3}
        onBack={handleBack}
        animationDelay={100}
      />

      <ScrollView
        style={styles.screenContainer}
        contentContainerStyle={styles.scrollContent}
      >
        <Animated.View style={[styles.content, contentAnimatedStyle]}>
          <AnimatedText
            variant="heading2"
            style={styles.title}
            delayMs={300}
            duration={600}
            direction="up"
          >
            Any allergies or dietary restrictions?
          </AnimatedText>

          <AnimatedText
            variant="bodyText"
            style={styles.description}
            delayMs={400}
            duration={600}
            direction="up"
          >
            We'll highlight these in your scan results.
          </AnimatedText>

          <View style={styles.allergiesContainer}>
            {commonAllergies.map((allergy, index) => (
              <Animated.View
                key={index}
                style={useAnimatedStyle(() => ({
                  opacity: allergyItemAnimations[index].value,
                  transform: [
                    { scale: interpolate(allergyItemAnimations[index].value, [0, 1], [0.9, 1]) }
                  ]
                }))}
              >
                <Tag
                  label={allergy}
                  selected={selectedAllergies.includes(allergy)}
                  onPress={() => handleAllergyToggle(allergy)}
                />
              </Animated.View>
            ))}
          </View>

          <Animated.View style={[styles.customAllergyContainer, inputAnimatedStyle]}>
            <Input
              placeholder="Add custom allergy or restriction"
              value={customAllergy}
              onChangeText={setCustomAllergy}
              returnKeyType="done"
              onSubmitEditing={handleAddCustomAllergy}
              rightIcon={
                <Ionicons
                  name="add-circle"
                  size={24}
                  color={Colors.DarkText}
                  onPress={handleAddCustomAllergy}
                />
              }
            />
          </Animated.View>

          {selectedAllergies.length > 0 && (
            <Animated.View style={[styles.selectedAllergiesContainer, selectedAnimatedStyle]}>
              <AnimatedText
                variant="description"
                style={styles.selectedAllergiesTitle}
                delayMs={0}
                duration={300}
              >
                Selected Allergies & Restrictions:
              </AnimatedText>
              <View style={styles.selectedAllergiesTags}>
                {selectedAllergies.map((allergy, index) => (
                  <Tag
                    key={index}
                    label={allergy}
                    selected
                    onRemove={() => handleAllergyToggle(allergy)}
                  />
                ))}
              </View>
            </Animated.View>
          )}
        </Animated.View>
      </ScrollView>

      <Animated.View style={[styles.footer, buttonAnimatedStyle]}>
        <AnimatedButton
          title="Next"
          onPress={handleNext}
          variant="primary"
          style={styles.button}
          glowOnPress={true}
          glowIntensity={0.4}
          scaleOnPress={true}
        />
      </Animated.View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.BackgroundPrimary,
    overflow: 'hidden',
  },
  backgroundElement: {
    position: 'absolute',
    bottom: -200,
    left: -150,
    width: 400,
    height: 400,
    borderRadius: 200,
    backgroundColor: Colors.DarkText,
    opacity: 0.05,
  },
  screenContainer: {
    flex: 1,
    backgroundColor: 'transparent',
  },
  scrollContent: {
    flexGrow: 1,
  },
  content: {
    flex: 1,
    paddingHorizontal: Spacing.Large,
    paddingTop: Spacing.Large,
    paddingBottom: Spacing.Large,
  },
  title: {
    marginBottom: Spacing.Medium,
  },
  description: {
    marginBottom: Spacing.Large,
    color: Colors.LightText,
  },
  allergiesContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    marginBottom: Spacing.Large,
  },
  customAllergyContainer: {
    marginBottom: Spacing.Large,
  },
  selectedAllergiesContainer: {
    marginBottom: Spacing.Large,
    backgroundColor: 'rgba(0, 0, 0, 0.02)',
    padding: Spacing.Medium,
    borderRadius: 12, // Medium border radius
  },
  selectedAllergiesTitle: {
    marginBottom: Spacing.Small,
    fontWeight: '500',
  },
  selectedAllergiesTags: {
    flexDirection: 'row',
    flexWrap: 'wrap',
  },
  footer: {
    paddingHorizontal: Spacing.Large,
    paddingBottom: Spacing.ExtraLarge,
  },
  button: {
    width: '100%',
  },
});

export default AllergiesRestrictionsScreen;
