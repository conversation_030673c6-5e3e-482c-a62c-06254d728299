import React from 'react';
import { View, StyleSheet } from 'react-native';
import { useNavigation, useRoute, RouteProp } from '@react-navigation/native';
import { NativeStackNavigationProp } from '@react-navigation/native-stack';
import { Ionicons } from '@expo/vector-icons';
import { Colors, Spacing } from '../theme';
import Typography from '../Components/Typography';
import Screen from '../Components/Screen';
import GlowingButton from '../Components/GlowingButton';
import StatusIndicator from '../Components/StatusIndicator';
import { RootStackParamList } from '../navigation/types';

type ScanErrorScreenNavigationProp = NativeStackNavigationProp<
  RootStackParamList,
  'ScanError'
>;

type ScanErrorScreenRouteProp = RouteProp<RootStackParamList, 'ScanError'>;

const ScanErrorScreen: React.FC = () => {
  const navigation = useNavigation<ScanErrorScreenNavigationProp>();
  const route = useRoute<ScanErrorScreenRouteProp>();
  const { errorCode, errorMessage, imageUri } = route.params;

  // Function to get dynamic title based on error code
  const getErrorTitle = (code: string): string => {
    switch (code) {
      case 'IRRELEVANT_CONTENT':
        return 'Irrelevant Content';
      case 'NETWORK_ERROR':
        return 'Network Error';
      case 'API_ERROR':
        return 'API Error';
      case 'INVALID_RESPONSE':
        return 'Invalid Response';
      case 'TECHNICAL_ERROR':
        return 'Technical Error';
      case 'UNKNOWN_ERROR':
        return 'Unknown Error';
      default:
        return 'Analysis Failed';
    }
  };

  const handleTryAgain = () => {
    navigation.navigate('Scan', { fromMainNav: true });
  };

  const handleGoBack = () => {
    navigation.goBack();
  };

  return (
    <Screen style={styles.container}>
      <View style={styles.content}>
        <StatusIndicator
          type="error"
          size="large"
          icon={<Ionicons name="alert" size={40} color={Colors.BackgroundPrimary} />}
          style={styles.statusIndicator}
        />
        <Typography variant="heading1" style={styles.title}>
          {getErrorTitle(errorCode)}
        </Typography>
        <Typography variant="bodyText" style={styles.description}>
          {errorMessage}
        </Typography>
      </View>
      <View style={styles.footer}>
        <GlowingButton
          title="Try Again"
          onPress={handleTryAgain}
          variant="primary"
          style={styles.button}
          glowIntensity={0.8}
        />
        <GlowingButton
          title="Go Back"
          onPress={handleGoBack}
          variant="secondary"
          style={styles.button}
        />
      </View>
    </Screen>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  content: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: Spacing.Large,
  },
  statusIndicator: {
    marginBottom: Spacing.Large,
  },
  title: {
    marginBottom: Spacing.Medium,
    textAlign: 'center',
  },
  description: {
    textAlign: 'center',
    marginBottom: Spacing.Large,
  },
  footer: {
    padding: Spacing.Large,
    gap: Spacing.Medium,
  },
  button: {
    width: '100%',
    marginBottom: Spacing.Medium,
  },
});

export default ScanErrorScreen;
