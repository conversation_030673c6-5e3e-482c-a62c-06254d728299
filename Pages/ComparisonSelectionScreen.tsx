import React, { useState, useEffect } from 'react';
import { View, StyleSheet, FlatList } from 'react-native';
import { useNavigation, useRoute } from '@react-navigation/native';
import { NativeStackNavigationProp } from '@react-navigation/native-stack';
import { Colors, Spacing } from '../theme';
import Screen from '../Components/Screen';
import Typography from '../Components/Typography';
import ListRow from '../Components/ListRow';
import Checkbox from '../Components/Checkbox';
import CustomButton from '../Components/CustomButton';
import BackButton from '../Components/BackButton';
import { Ionicons } from '@expo/vector-icons';

// Define the navigation param list
type RootStackParamList = {
  History: undefined;
  ComparisonSelection: { currentProductId?: string };
  ComparisonView: { selectedProductIds: string[] };
};

type ComparisonSelectionScreenNavigationProp = NativeStackNavigationProp<
  RootStackParamList,
  'ComparisonSelection'
>;

type RouteParams = {
  currentProductId?: string;
};

// Mock data for history items
interface HistoryItem {
  id: string;
  productName: string;
  date: string;
  grade: 'A' | 'B' | 'C' | 'D' | 'E';
}

const mockHistoryItems: HistoryItem[] = [
  {
    id: '1',
    productName: 'Organic Granola',
    date: '2023-05-15',
    grade: 'A',
  },
  {
    id: '2',
    productName: 'Almond Milk',
    date: '2023-05-14',
    grade: 'B',
  },
  {
    id: '3',
    productName: 'Protein Bar',
    date: '2023-05-12',
    grade: 'C',
  },
  {
    id: '4',
    productName: 'Chocolate Cookies',
    date: '2023-05-10',
    grade: 'D',
  },
  {
    id: '5',
    productName: 'Energy Drink',
    date: '2023-05-08',
    grade: 'E',
  },
  {
    id: '6',
    productName: 'Coconut Water',
    date: '2023-05-05',
    grade: 'A',
  },
  {
    id: '7',
    productName: 'Whole Wheat Bread',
    date: '2023-05-03',
    grade: 'B',
  },
];

const ComparisonSelectionScreen: React.FC = () => {
  const navigation = useNavigation<ComparisonSelectionScreenNavigationProp>();
  const route = useRoute();
  const { currentProductId } = (route.params as RouteParams) || {};

  // State for selected items
  const [selectedItems, setSelectedItems] = useState<string[]>(
    currentProductId ? [currentProductId] : []
  );

  // Function to toggle item selection
  const toggleItemSelection = (id: string) => {
    setSelectedItems((prevSelected) => {
      if (prevSelected.includes(id)) {
        return prevSelected.filter((itemId) => itemId !== id);
      } else {
        return [...prevSelected, id];
      }
    });
  };

  // Get grade color based on grade
  const getGradeColor = (grade: 'A' | 'B' | 'C' | 'D' | 'E') => {
    switch (grade) {
      case 'A':
        return Colors.GradeA;
      case 'B':
        return Colors.GradeB;
      case 'C':
        return Colors.GradeC;
      case 'D':
        return Colors.GradeD;
      case 'E':
        return Colors.GradeE;
      default:
        return Colors.LightText;
    }
  };

  // Render item
  const renderItem = ({ item }: { item: HistoryItem }) => {
    const isSelected = selectedItems.includes(item.id);
    const isCurrentProduct = item.id === currentProductId;

    // Format date
    const formattedDate = new Date(item.date).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
    });

    return (
      <ListRow
        title={item.productName}
        description={formattedDate}
        leftIcon={
          <View style={styles.gradeContainer}>
            <Typography
              variant="bodyText"
              color={getGradeColor(item.grade)}
              style={styles.gradeText}
            >
              {item.grade}
            </Typography>
          </View>
        }
        rightIcon={
          <Checkbox
            checked={isSelected}
            onPress={() => toggleItemSelection(item.id)}
            size={24}
          />
        }
        onPress={() => toggleItemSelection(item.id)}
        style={isCurrentProduct ? styles.currentProduct : undefined}
      />
    );
  };

  // Handle back button press
  const handleBack = () => {
    navigation.goBack();
  };

  // Handle compare button press
  const handleCompare = () => {
    navigation.navigate('ComparisonView', { selectedProductIds: selectedItems });
  };

  return (
    <Screen scrollable={false} style={styles.screen}>
      <View style={styles.header}>
        <BackButton onPress={handleBack} />
        <Typography variant="heading1" style={styles.title}>
          Compare Products
        </Typography>
      </View>

      <Typography variant="bodyText" style={styles.instructions}>
        Select products to compare side by side. Choose up to 3 products for the best comparison experience.
      </Typography>

      <FlatList
        data={mockHistoryItems}
        renderItem={renderItem}
        keyExtractor={(item) => item.id}
        contentContainerStyle={styles.listContent}
        showsVerticalScrollIndicator={false}
      />

      <View style={styles.footer}>
        <CustomButton
          title={`Compare Selected (${selectedItems.length})`}
          onPress={handleCompare}
          disabled={selectedItems.length < 2}
          style={styles.compareButton}
        />
      </View>
    </Screen>
  );
};

const styles = StyleSheet.create({
  screen: {
    backgroundColor: Colors.BackgroundPrimary,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: Spacing.Large,
    paddingTop: Spacing.Large,
    paddingBottom: Spacing.Medium,
  },
  title: {
    flex: 1,
    marginLeft: Spacing.Medium,
  },
  instructions: {
    paddingHorizontal: Spacing.Large,
    marginBottom: Spacing.Large,
  },
  listContent: {
    paddingBottom: Spacing.XXLarge,
  },
  gradeContainer: {
    width: 30,
    height: 30,
    borderRadius: 15,
    backgroundColor: Colors.SurfaceSecondary,
    alignItems: 'center',
    justifyContent: 'center',
  },
  gradeText: {
    fontWeight: '700',
  },
  currentProduct: {
    backgroundColor: Colors.SurfaceSecondary,
  },
  footer: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    backgroundColor: Colors.BackgroundPrimary,
    paddingHorizontal: Spacing.Large,
    paddingVertical: Spacing.Medium,
    borderTopWidth: 1,
    borderTopColor: Colors.SurfaceSecondary,
  },
  compareButton: {
    width: '100%',
  },
});

export default ComparisonSelectionScreen;
