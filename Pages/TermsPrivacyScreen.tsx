import React, { useState } from 'react';
import { View, StyleSheet, ScrollView } from 'react-native';
import { useNavigation, useRoute, RouteProp } from '@react-navigation/native';
import { NativeStackNavigationProp } from '@react-navigation/native-stack';
import { Colors, Spacing } from '../theme';
import Screen from '../Components/Screen';
import Typography from '../Components/Typography';
import CustomButton from '../Components/CustomButton';
import BackButton from '../Components/BackButton';
import Checkbox from '../Components/Checkbox';

type RootStackParamList = {
  TermsPrivacy: { imageUri: string };
  ScanResults: { imageUri: string };
  PhoneNumberLogin: { imageUri: string };
  CreatingAccount: undefined;
};

type TermsPrivacyScreenNavigationProp = NativeStackNavigationProp<
  RootStackParamList,
  'TermsPrivacy'
>;

type TermsPrivacyScreenRouteProp = RouteProp<RootStackParamList, 'TermsPrivacy'>;

const TermsPrivacyScreen: React.FC = () => {
  const navigation = useNavigation<TermsPrivacyScreenNavigationProp>();
  const route = useRoute<TermsPrivacyScreenRouteProp>();
  const [isChecked, setIsChecked] = useState(false);

  const handleBack = () => {
    navigation.goBack();
  };

  const handleAgree = () => {
    navigation.navigate('PhoneNumberLogin', { imageUri: route.params.imageUri });
  };

  const toggleCheckbox = () => {
    setIsChecked(!isChecked);
  };

  return (
    <Screen scrollable style={styles.container}>
      <View style={styles.header}>
        <BackButton onPress={handleBack} />
      </View>
      <View style={styles.content}>
        <Typography variant="heading1" style={styles.title}>
          Agree to Terms
        </Typography>
        <Typography variant="bodyText" style={styles.description}>
          Before creating your account, please read and agree to our Terms of Service and Privacy Policy.
        </Typography>
        <View style={styles.termsContainer}>
          <ScrollView style={styles.termsScroll}>
            <Typography variant="description" style={styles.termsText}>
              {`Terms of Service and Privacy Policy

1. Introduction
Welcome to JunkChk. By using our app, you agree to these Terms of Service and our Privacy Policy.

2. Use of Service
JunkChk provides a service that allows users to scan and analyze product ingredients. We do not guarantee the accuracy of our analysis and it should not be used as medical advice.

3. User Accounts
You are responsible for maintaining the confidentiality of your account information and for all activities that occur under your account.

4. Privacy
We collect and process personal data as described in our Privacy Policy. By using JunkChk, you consent to our data practices.

5. Content
You retain ownership of any content you submit to JunkChk. By submitting content, you grant us a license to use it for operating and improving our services.

6. Termination
We may terminate or suspend your account at any time for any reason without notice.

7. Disclaimer of Warranties
JunkChk is provided "as is" without warranties of any kind.

8. Limitation of Liability
We shall not be liable for any indirect, incidental, special, consequential, or punitive damages.

9. Changes to Terms
We may modify these terms at any time. Your continued use of JunkChk constitutes acceptance of the modified terms.

10. Governing Law
These terms shall be governed by the laws of the jurisdiction in which we operate.`}
            </Typography>
          </ScrollView>
        </View>
        <View style={styles.checkboxContainer}>
          <Checkbox checked={isChecked} onPress={toggleCheckbox} style={styles.checkbox} />
          <Typography variant="bodyText" style={styles.checkboxLabel}>
            I agree to the Terms of Service and Privacy Policy
          </Typography>
        </View>
      </View>
      <View style={styles.footer}>
        <CustomButton
          title="Agree & Continue"
          onPress={handleAgree}
          variant="primary"
          style={styles.button}
          disabled={!isChecked}
          textStyle={{ fontWeight: 'bold' }}
        />
      </View>
    </Screen>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.BackgroundPrimary,
  },
  header: {
    paddingTop: Spacing.Medium,
    paddingBottom: Spacing.Medium,
  },
  content: {
    flex: 1,
    paddingBottom: 100, // Add padding to account for the fixed button at bottom
  },
  title: {
    marginBottom: Spacing.Medium,
  },
  description: {
    marginBottom: Spacing.Large,
  },
  termsContainer: {
    height: 300,
    marginBottom: Spacing.Large,
    borderWidth: 1,
    borderColor: Colors.SurfaceSecondary,
    borderRadius: 8,
  },
  termsScroll: {
    padding: Spacing.Medium,
  },
  termsText: {
    lineHeight: 20,
  },
  checkboxContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: Spacing.Large,
  },
  checkbox: {
    marginRight: Spacing.Medium,
  },
  checkboxLabel: {
    flex: 1,
  },
  footer: {
    paddingHorizontal: Spacing.Large,
    paddingBottom: Spacing.ExtraLarge,
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
  },
  button: {
    width: '100%',
    height: 56, // Taller for better appearance
    borderRadius: 28, // More rounded corners
    shadowColor: '#000',
    shadowOpacity: 0.3,
    shadowRadius: 12,
    elevation: 15,
  },
});

export default TermsPrivacyScreen;
