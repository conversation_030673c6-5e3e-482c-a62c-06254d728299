import React, { useEffect, useState, memo, useCallback, useMemo } from 'react';
import { View, StyleSheet, ActivityIndicator, BackHandler, Alert } from 'react-native';
import { useNavigation, useRoute, RouteProp, useFocusEffect } from '@react-navigation/native';
import { NativeStackNavigationProp } from '@react-navigation/native-stack';
import { useAuth, usePersonalization } from '../context';
import NetInfo from '@react-native-community/netinfo';

import { Colors, Spacing } from '../theme';
import Screen from '../Components/Screen';
import Typography from '../Components/Typography';
import SegmentedProgressIndicator from '../Components/SegmentedProgressIndicator';
import { Ionicons } from '@expo/vector-icons';
import * as FileSystem from 'expo-file-system';
import * as ImageManipulator from 'expo-image-manipulator';
import serverApiService from '../src/services/serverApiService';
import { RootStackParamList } from '../navigation/types';
import { useHighRefreshRate } from '../src/hooks/useHighRefreshRate';
import PerformanceMonitor from '../Components/PerformanceMonitor';
import ErrorBoundary from '../Components/ErrorBoundary';

// Define the route params type
type ProcessingScreenParams = {
  photoUri: string;
  fromMainNav?: boolean;
  isAuthenticated?: boolean;
};

type ProcessingScreenNavigationProp = NativeStackNavigationProp<RootStackParamList, 'Processing'>;
type ProcessingScreenRouteProp = RouteProp<{ Processing: ProcessingScreenParams }, 'Processing'>;

const ProcessingScreen: React.FC = memo(() => {
  const navigation = useNavigation<ProcessingScreenNavigationProp>();
  const route = useRoute<ProcessingScreenRouteProp>();
  const { authState } = useAuth();
  const { personalizationState } = usePersonalization();

  // Enable high refresh rate optimizations
  useHighRefreshRate();

  const [currentPhase, setCurrentPhase] = useState(0);
  const [apiResponse, setApiResponse] = useState<any>(null);
  const [error, setError] = useState<string | null>(null);

  // Dynamic content for our food analysis app
  const mainTitles = [
    'Analyzing Product Image',
    'Processing Ingredients',
    'Generating Results'
  ];

  const subtitles = [
    'Scanning product details',
    'Analyzing nutritional content',
    'Finalizing report'
  ];

  const statusTexts = [
    'Detecting product',
    'Analyzing ingredients',
    'Calculating nutrition score'
  ];

  const statusSubtexts = [
    'Identifying product type',
    'Processing nutritional data',
    'Preparing final analysis'
  ];

  // Back button is disabled for this screen
  const handleBack = () => {
    // Do nothing - prevent going back
    return;
  };

  // Prevent hardware back button on Android
  useFocusEffect(
    React.useCallback(() => {
      const onBackPress = () => {
        // Return true to prevent default behavior (going back)
        return true;
      };

      // Add event listener
      const subscription = BackHandler.addEventListener('hardwareBackPress', onBackPress);

      // Clean up event listener on unmount
      return () => subscription.remove();
    }, [])
  );

  // Image processing functions - memoized with improved error handling
  const resizeImage = useCallback(async (uri: string) => {
    try {
      console.log('ProcessingScreen: Starting image resize for:', uri);

      // Verify the source image exists
      const fileInfo = await FileSystem.getInfoAsync(uri);
      if (!fileInfo.exists) {
        console.error('Source image file does not exist');
        throw new Error('Source image file does not exist');
      }

      // Set a timeout for image manipulation to prevent hanging
      const manipulationPromise = ImageManipulator.manipulateAsync(
        uri,
        [{ resize: { width: 1024 } }],
        { compress: 0.8, format: ImageManipulator.SaveFormat.JPEG }
      );

      const timeoutPromise = new Promise((_, reject) =>
        setTimeout(() => reject(new Error('Image manipulation timed out')), 45000)
      );

      // Race the manipulation against the timeout
      const manipResult = await Promise.race([manipulationPromise, timeoutPromise]) as ImageManipulator.ImageResult;

      if (!manipResult || !manipResult.uri) {
        throw new Error('Image manipulation failed - no result returned');
      }

      console.log('ProcessingScreen: Image resize completed successfully');
      return manipResult.uri;
    } catch (error) {
      console.error('Error resizing image:', error);
      // Return original URI as fallback instead of throwing
      console.log('ProcessingScreen: Using original image as fallback');
      return uri;
    }
  }, []);

  const getBase64Image = useCallback(async (uri: string) => {
    try {
      const resizedUri = await resizeImage(uri);
      const base64Image = await FileSystem.readAsStringAsync(resizedUri, {
        encoding: FileSystem.EncodingType.Base64,
      });
      return base64Image;
    } catch (error) {
      console.error('Error converting image to base64:', error);
      throw new Error('Failed to process image');
    }
  }, [resizeImage]);

  useEffect(() => {
    // Get the photo URI from the route params
    const photoUri = route.params?.photoUri;
    if (!photoUri) {
      setError('No image provided');
      return;
    }

    // Simulate processing time - total 18 seconds
    const totalDuration = 18000; // 18 seconds

    // Equal phase intervals - 6 seconds each
    const phase1Duration = 6000; // 6 seconds
    const phase2Duration = 6000; // 6 seconds
    const phase3Duration = 6000; // 6 seconds

    // Phase 1
    const phase1Timer = setTimeout(() => {
      setCurrentPhase(1);
    }, phase1Duration);

    // Phase 2
    const phase2Timer = setTimeout(() => {
      setCurrentPhase(2);
    }, phase1Duration + phase2Duration);

    // Process the image and call the API
    const processImage = async () => {
      try {
        // Check network connectivity first
        const netInfoState = await NetInfo.fetch();
        console.log('Network state:', netInfoState);

        if (!netInfoState.isConnected) {
          console.error('No internet connection');
          navigation.navigate('ScanError', {
            errorCode: 'NETWORK_ERROR',
            errorMessage: 'No internet connection. Please check your network settings and try again.',
            imageUri: photoUri
          });
          return;
        }

        // Test server connectivity with detailed logging
        try {
          console.log('Testing server connectivity to:', serverApiService.baseUrl);
          const healthResponse = await serverApiService.checkHealth();
          console.log('Server connectivity test successful:', healthResponse);
        } catch (networkError) {
          console.error('Server connectivity test failed:', networkError);
          console.error('Server URL being used:', serverApiService.baseUrl);
          console.error('Network error details:', {
            message: networkError.message,
            code: networkError.code,
            stack: networkError.stack
          });

          navigation.navigate('ScanError', {
            errorCode: 'NETWORK_ERROR',
            errorMessage: `Cannot connect to analysis server at ${serverApiService.baseUrl}. Please check your internet connection and try again.`,
            imageUri: photoUri
          });
          return;
        }

        // Convert the image to base64
        const resizedUri = await resizeImage(photoUri);
        const base64Image = await FileSystem.readAsStringAsync(resizedUri, {
          encoding: FileSystem.EncodingType.Base64,
        });

        // Call the server API for image analysis
        console.log('Calling server API for image analysis...');
        let results: any;
        try {
          results = await serverApiService.analyzeSingleImage(base64Image, personalizationState.allergiesRestrictions || []);
          console.log('Server API call successful');
        } catch (apiError) {
          console.error('Server API call error:', apiError);

          // Map server error codes to navigation
          let errorCode = 'API_ERROR';
          if (apiError.code === 'NETWORK_ERROR') {
            errorCode = 'NETWORK_ERROR';
          } else if (apiError.code === 'TIMEOUT_ERROR') {
            errorCode = 'TIMEOUT_ERROR';
          }

          navigation.navigate('ScanError', {
            errorCode: errorCode,
            errorMessage: apiError.message || 'Error calling the server API',
            imageUri: photoUri
          });
          return;
        }

        // The server already returns parsed JSON, so we can use it directly
        console.log('Server response:', results);

        // Validate the response structure
        if (!results || typeof results !== 'object') {
          console.error('Invalid response structure:', results);
          navigation.navigate('ScanError', {
            errorCode: 'TECHNICAL_ERROR',
            errorMessage: 'Invalid response format received. Please try again later.',
            imageUri: photoUri
          });
          return;
        }

        // Check if the response contains an error
        if (results.error) {
          console.log('Server returned error:', results.error);
          navigation.navigate('ScanError', {
            errorCode: results.error.code || 'API_ERROR',
            errorMessage: results.error.message || 'Analysis failed. Please try again.',
            imageUri: photoUri
          });
          return;
        }

        // Set the API response
        setApiResponse(results);

        // Navigate to the results screen after the total duration
        const completeTimer = setTimeout(() => {
          // Always use the current authentication state from context
          // This ensures we're using the most up-to-date authentication state
          const isAuthenticated = authState.isAuthenticated;

          console.log('ProcessingScreen: navigating to ScanResults, isAuthenticated=', isAuthenticated);

          navigation.navigate('ScanResults', {
            imageUri: photoUri,
            isAnonymous: false, // We'll let ScanResultsScreen determine this from authState
            apiResponse: results
          });
        }, totalDuration - (Date.now() - startTime)); // Adjust remaining time

        return () => clearTimeout(completeTimer);
      } catch (error) {
        console.error('Error processing image:', error);
        setError('Error processing image. Please try again.');

        // Navigate to error screen with technical error
        navigation.navigate('ScanError', {
          errorCode: 'TECHNICAL_ERROR',
          errorMessage: `An error occurred while analyzing the image: ${error.message || 'Unknown error'}. Please try again later.`,
          imageUri: photoUri
        });
      }
    };

    const startTime = Date.now();
    processImage();

    return () => {
      clearTimeout(phase1Timer);
      clearTimeout(phase2Timer);
    };
  }, [navigation, route.params]);

  return (
    <ErrorBoundary>
      <PerformanceMonitor componentName="ProcessingScreen">
        <Screen style={styles.container}>
          {/* Back button removed to prevent navigation */}
          <View style={styles.content}>
            <View style={styles.progressSection}>
              <SegmentedProgressIndicator
                size={220}
                strokeWidth={10}
                duration={18000}
                segments={3}
                currentSegment={currentPhase}
                showText={false}
              />

              {/* Step indicator dots - dynamic based on current phase */}
              <View style={styles.dotsContainer}>
                {[0, 1, 2].map((index) => (
                  <View
                    key={`dot-${index}`}
                    style={[
                      styles.dot,
                      index === currentPhase && styles.activeDot,
                      index < currentPhase && styles.completedDot
                    ]}
                  />
                ))}
              </View>

              <Typography variant="description" style={styles.stepText}>
                Step {currentPhase + 1}
              </Typography>
              <Typography variant="heading1" style={styles.title}>
                {mainTitles[currentPhase]}
              </Typography>
              <Typography variant="description" style={styles.subtitle}>
                {subtitles[currentPhase]}
              </Typography>
            </View>

            {/* Dynamic status item based on current phase */}
            <View style={styles.statusContainer}>
              <View style={[styles.statusItem, styles.activeStatusItem]}>
                <View style={styles.statusIconContainer}>
                  <ActivityIndicator size="small" color={Colors.DarkText} style={styles.statusLoader} />
                </View>
                <View style={styles.statusTextContainer}>
                  <Typography variant="description" style={styles.activeStatusText}>
                    {statusTexts[currentPhase]}
                  </Typography>
                  <Typography variant="caption" style={styles.statusSubtext}>
                    {statusSubtexts[currentPhase]}
                  </Typography>
                </View>
              </View>
            </View>
          </View>
        </Screen>
      </PerformanceMonitor>
    </ErrorBoundary>
  );
});

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F8F9FA',
  },
  content: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    paddingHorizontal: Spacing.Large,
    paddingVertical: Spacing.ExtraLarge,
  },
  progressSection: {
    alignItems: 'center',
    justifyContent: 'center',
    flex: 1,
    marginBottom: Spacing.ExtraLarge,
  },
  dotsContainer: {
    flexDirection: 'row',
    marginTop: Spacing.Large,
    marginBottom: Spacing.Medium,
  },
  dot: {
    width: 8,
    height: 8,
    borderRadius: 4,
    backgroundColor: 'rgba(255, 255, 255, 0.4)',
    marginHorizontal: 4,
    borderWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.6)',
  },
  activeDot: {
    backgroundColor: 'rgba(0, 0, 0, 0.8)',
    width: 8,
    height: 8,
    borderRadius: 4,
    borderWidth: 1,
    borderColor: 'rgba(0, 0, 0, 0.2)',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 4,
    elevation: 4,
  },
  completedDot: {
    backgroundColor: 'rgba(0, 0, 0, 0.6)',
    borderColor: 'rgba(0, 0, 0, 0.3)',
  },
  stepText: {
    textAlign: 'center',
    color: Colors.LightText,
    fontSize: 14,
    marginBottom: Spacing.Small,
  },
  title: {
    textAlign: 'center',
    fontWeight: 'bold',
    fontSize: 24,
    color: Colors.DarkText,
    marginBottom: Spacing.ExtraSmall,
  },
  subtitle: {
    textAlign: 'center',
    color: Colors.LightText,
    fontSize: 16,
    marginTop: Spacing.ExtraSmall,
  },
  statusContainer: {
    width: '100%',
    paddingHorizontal: Spacing.Medium,
    marginTop: Spacing.Large,
  },
  statusItem: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    marginBottom: Spacing.Large,
    opacity: 0.4,
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    borderRadius: 16,
    padding: Spacing.Large,
    borderWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.2)',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.1,
    shadowRadius: 12,
    elevation: 8,
  },
  activeStatusItem: {
    opacity: 1,
    backgroundColor: 'rgba(255, 255, 255, 0.25)',
    borderWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.3)',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 8 },
    shadowOpacity: 0.15,
    shadowRadius: 20,
    elevation: 12,
  },
  completedStatusItem: {
    opacity: 0.8,
    backgroundColor: 'rgba(255, 255, 255, 0.15)',
    borderColor: 'rgba(255, 255, 255, 0.25)',
  },
  statusIconContainer: {
    width: 24,
    height: 24,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: Spacing.Medium,
    marginTop: 2,
  },
  statusLoader: {
    // No additional margin needed
  },
  statusIcon: {
    // No additional margin needed
  },
  statusPlaceholder: {
    width: 20,
    height: 20,
    borderRadius: 10,
    backgroundColor: Colors.SurfaceSecondary,
  },
  statusTextContainer: {
    flex: 1,
  },
  statusText: {
    color: Colors.LightText,
    fontSize: 16,
    fontWeight: '500',
  },
  activeStatusText: {
    color: Colors.DarkText,
    fontWeight: '600',
    fontSize: 16,
  },
  completedStatusText: {
    color: Colors.Success,
    fontWeight: '500',
    fontSize: 16,
  },
  statusSubtext: {
    color: Colors.LightText,
    fontSize: 14,
    marginTop: 4,
    opacity: 0.8,
  },
});

export default ProcessingScreen;
