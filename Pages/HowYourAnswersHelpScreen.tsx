import React from 'react';
import { View, StyleSheet, SafeAreaView } from 'react-native';
import { useNavigation } from '@react-navigation/native';
import { NativeStackNavigationProp } from '@react-navigation/native-stack';
import { Colors, Spacing, Shadow } from '../theme';
import Screen from '../Components/Screen';
import Typography from '../Components/Typography';
import CustomButton from '../Components/CustomButton';
import GlowingButton from '../Components/GlowingButton';
import BackButton from '../Components/BackButton';
import { Ionicons } from '@expo/vector-icons';
import { usePersonalization } from '../context';

type RootStackParamList = {
  HowYourAnswersHelp: undefined;
  AllergiesRestrictions: undefined;
  HowJunkChkWorks: undefined;
};

type HowYourAnswersHelpScreenNavigationProp = NativeStackNavigationProp<
  RootStackParamList,
  'HowYourAnswersHelp'
>;

const HowYourAnswersHelpScreen: React.FC = () => {
  const navigation = useNavigation<HowYourAnswersHelpScreenNavigationProp>();
  const { completePersonalization } = usePersonalization();

  const handleBack = () => {
    navigation.navigate('AllergiesRestrictions');
  };

  const handleNext = async () => {
    // Mark personalization as complete
    await completePersonalization();
    navigation.navigate('HowJunkChkWorks');
  };

  return (
    <Screen style={styles.container}>
      <SafeAreaView style={styles.safeArea}>
        <View style={styles.header}>
          <BackButton onPress={handleBack} />
        </View>
      </SafeAreaView>
      <View style={styles.content}>
        <Typography variant="heading2" style={styles.title}>
          Great! Here's how we'll use your answers:
        </Typography>

        <View style={styles.benefitsContainer}>
          <View style={styles.benefitItem}>
            <View style={styles.iconContainer}>
              <Ionicons name="warning-outline" size={28} color={Colors.BackgroundPrimary} />
            </View>
            <View style={styles.textContainer}>
              <Typography variant="bodyText" style={styles.benefitTitle}>
                Allergen Detection
              </Typography>
              <Typography variant="description" style={styles.benefitDescription}>
                Instantly see if a product contains your allergens
              </Typography>
            </View>
          </View>

          <View style={styles.benefitItem}>
            <View style={styles.iconContainer}>
              <Ionicons name="analytics-outline" size={28} color={Colors.BackgroundPrimary} />
            </View>
            <View style={styles.textContainer}>
              <Typography variant="bodyText" style={styles.benefitTitle}>
                Personalized Insights
              </Typography>
              <Typography variant="description" style={styles.benefitDescription}>
                Get insights relevant to your health goals
              </Typography>
            </View>
          </View>

          <View style={styles.benefitItem}>
            <View style={styles.iconContainer}>
              <Ionicons name="sparkles-outline" size={28} color={Colors.BackgroundPrimary} />
            </View>
            <View style={styles.textContainer}>
              <Typography variant="bodyText" style={styles.benefitTitle}>
                Smart Recommendations
              </Typography>
              <Typography variant="description" style={styles.benefitDescription}>
                Receive recommendations based on your preferences
              </Typography>
            </View>
          </View>

          <View style={styles.benefitItem}>
            <View style={styles.iconContainer}>
              <Ionicons name="eye-outline" size={28} color={Colors.BackgroundPrimary} />
            </View>
            <View style={styles.textContainer}>
              <Typography variant="bodyText" style={styles.benefitTitle}>
                Ingredient Focus
              </Typography>
              <Typography variant="description" style={styles.benefitDescription}>
                Highlight ingredients that matter most to you
              </Typography>
            </View>
          </View>
        </View>
      </View>
      <View style={styles.footer}>
        <GlowingButton
          title="Show me how JunkChk works"
          onPress={handleNext}
          variant="primary"
          style={styles.button}
          glowIntensity={0.6}
          alwaysGlow={true}
        />
      </View>
    </Screen>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.BackgroundPrimary,
  },
  safeArea: {
    backgroundColor: Colors.BackgroundPrimary,
    borderBottomWidth: 1,
    borderBottomColor: Colors.SurfaceSecondary,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: Spacing.Large,
    paddingTop: Spacing.Small,
    paddingBottom: Spacing.Medium,
    backgroundColor: Colors.BackgroundPrimary,
  },
  content: {
    flex: 1,
    paddingHorizontal: Spacing.Large,
    paddingTop: Spacing.Large,
  },
  title: {
    marginBottom: Spacing.Large,
    textAlign: 'center',
  },
  benefitsContainer: {
    marginTop: Spacing.Medium,
  },
  benefitItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: Spacing.Medium,
  },
  iconContainer: {
    width: 48,
    height: 48,
    borderRadius: 24,
    backgroundColor: Colors.DarkText,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: Spacing.Medium,
  },
  textContainer: {
    flex: 1,
  },
  benefitTitle: {
    fontWeight: '600',
    marginBottom: Spacing.ExtraSmall,
  },
  benefitDescription: {
    lineHeight: 20,
  },
  footer: {
    paddingHorizontal: Spacing.Large,
    paddingBottom: Spacing.ExtraLarge,
  },
  button: {
    width: '100%',
  },
});

export default HowYourAnswersHelpScreen;
