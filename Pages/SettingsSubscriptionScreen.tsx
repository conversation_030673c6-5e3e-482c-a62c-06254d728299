import React from 'react';
import { View, StyleSheet, ScrollView, TouchableOpacity } from 'react-native';
import { useNavigation } from '@react-navigation/native';
import { NativeStackNavigationProp } from '@react-navigation/native-stack';
import { Colors, Spacing } from '../theme';
import Screen from '../Components/Screen';
import Typography from '../Components/Typography';
import Card from '../Components/Card';
import BackButton from '../Components/BackButton';
import CustomButton from '../Components/CustomButton';
import { Ionicons } from '@expo/vector-icons';

type RootStackParamList = {
  Settings: undefined;
  SettingsSubscription: undefined;
};

type SettingsSubscriptionScreenNavigationProp = NativeStackNavigationProp<
  RootStackParamList,
  'SettingsSubscription'
>;

// Premium benefits list
const premiumBenefits = [
  'Ad-free experience',
  'Unlimited scan history',
  'Detailed ingredient analysis',
  'Compare up to 5 products at once',
  'Export scan results',
  'Priority customer support',
];

const SettingsSubscriptionScreen: React.FC = () => {
  const navigation = useNavigation<SettingsSubscriptionScreenNavigationProp>();

  // Mock subscription status - in a real app, this would come from a state or API
  const isSubscribed = false;
  const subscriptionType = isSubscribed ? 'Premium' : 'Free';

  const handleBack = () => {
    navigation.navigate('Settings');
  };

  const handleUpgrade = () => {
    // In a real app, this would open a subscription purchase flow
    console.log('Upgrade to premium');
  };

  const handleManage = () => {
    // In a real app, this would open subscription management
    console.log('Manage subscription');
  };

  return (
    <Screen style={styles.container}>
      <View style={styles.header}>
        <BackButton onPress={handleBack} />
        <Typography variant="heading1" style={styles.title}>
          Subscription
        </Typography>
      </View>

      <ScrollView style={styles.content} contentContainerStyle={styles.scrollContent}>
        {/* Current Subscription Status */}
        <Card style={styles.statusCard}>
          <View style={styles.statusHeader}>
            <Typography variant="heading2" style={styles.statusTitle}>
              Current Plan
            </Typography>
            <View style={[
              styles.statusBadge,
              isSubscribed ? styles.premiumBadge : styles.freeBadge
            ]}>
              <Typography
                variant="description"
                color={isSubscribed ? Colors.BackgroundPrimary : Colors.DarkText}
                style={styles.statusBadgeText}
              >
                {subscriptionType}
              </Typography>
            </View>
          </View>

          <Typography variant="bodyText" style={styles.statusDescription}>
            {isSubscribed
              ? 'You are currently on the Premium plan. Enjoy all the premium features!'
              : 'You are currently on the Free plan. Upgrade to Premium to unlock all features.'}
          </Typography>
        </Card>

        {/* Premium Benefits */}
        <Typography variant="heading2" style={styles.benefitsTitle}>
          Premium Benefits
        </Typography>
        <Card style={styles.benefitsCard}>
          {premiumBenefits.map((benefit, index) => (
            <View key={index} style={styles.benefitRow}>
              <Ionicons
                name="checkmark-circle"
                size={20}
                color={Colors.Success}
                style={styles.benefitIcon}
              />
              <Typography variant="bodyText" style={styles.benefitText}>
                {benefit}
              </Typography>
            </View>
          ))}
        </Card>

        {/* Action Buttons */}
        <View style={styles.actionContainer}>
          {isSubscribed ? (
            <CustomButton
              title="Manage Subscription"
              onPress={handleManage}
              variant="primary"
              style={styles.actionButton}
            />
          ) : (
            <CustomButton
              title="Upgrade to Premium"
              onPress={handleUpgrade}
              variant="primary"
              style={styles.actionButton}
            />
          )}
        </View>
      </ScrollView>
    </Screen>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.BackgroundPrimary,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: Spacing.Large,
    paddingTop: Spacing.Large,
    paddingBottom: Spacing.Medium,
  },
  title: {
    marginLeft: Spacing.Medium,
  },
  content: {
    flex: 1,
  },
  scrollContent: {
    paddingHorizontal: Spacing.Large,
    paddingBottom: Spacing.XXLarge,
  },
  statusCard: {
    marginBottom: Spacing.Large,
  },
  statusHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: Spacing.Medium,
  },
  statusTitle: {
    fontWeight: '600',
  },
  statusBadge: {
    paddingVertical: Spacing.ExtraSmall,
    paddingHorizontal: Spacing.Small,
    borderRadius: 8, // Small border radius
  },
  freeBadge: {
    backgroundColor: Colors.SurfaceSecondary,
  },
  premiumBadge: {
    backgroundColor: Colors.AccentBlue,
  },
  statusBadgeText: {
    fontWeight: '600',
  },
  statusDescription: {
    color: Colors.LightText,
  },
  benefitsTitle: {
    marginBottom: Spacing.Medium,
  },
  benefitsCard: {
    marginBottom: Spacing.Large,
  },
  benefitRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: Spacing.Medium,
  },
  benefitIcon: {
    marginRight: Spacing.Small,
  },
  benefitText: {
    flex: 1,
  },
  actionContainer: {
    marginTop: Spacing.Medium,
  },
  actionButton: {
    width: '100%',
  },
});

export default SettingsSubscriptionScreen;
