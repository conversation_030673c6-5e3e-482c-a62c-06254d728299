import React from 'react';
import { View, StyleSheet, TouchableOpacity, ScrollView } from 'react-native';
import { useNavigation } from '@react-navigation/native';
import { NativeStackNavigationProp } from '@react-navigation/native-stack';
import { Colors, Spacing } from '../theme';
import Screen from '../Components/Screen';
import Typography from '../Components/Typography';
import ListRow from '../Components/ListRow';
import { Ionicons } from '@expo/vector-icons';
import { useAuth } from '../context';

type RootStackParamList = {
  Settings: undefined;
  SettingsAccount: undefined;
  SettingsAllergies: undefined;
  SettingsSubscription: undefined;
  SettingsSupport: undefined;
  SettingsAbout: undefined;
  History: undefined;
  Home: undefined;
};

type SettingsScreenNavigationProp = NativeStackNavigationProp<
  RootStackParamList,
  'Settings'
>;

const SettingsScreen: React.FC = () => {
  const navigation = useNavigation<SettingsScreenNavigationProp>();
  const { authState } = useAuth();

  // Check if phone is linked based on authentication state
  const isPhoneLinked = authState.isAuthenticated && authState.user?.phoneNumber;

  const handleClose = () => {
    navigation.navigate('Home');
  };

  const navigateTo = (screen: keyof RootStackParamList) => {
    navigation.navigate(screen);
  };

  // Settings options with icons
  const settingsOptions = [
    {
      id: 'account',
      title: 'Account',
      icon: <Ionicons name="person-outline" size={24} color={Colors.DarkText} />,
      screen: 'SettingsAccount' as keyof RootStackParamList,
    },
    {
      id: 'allergies',
      title: 'Allergies & Restrictions',
      icon: <Ionicons name="warning-outline" size={24} color={Colors.DarkText} />,
      screen: 'SettingsAllergies' as keyof RootStackParamList,
    },
    {
      id: 'subscription',
      title: 'Subscription',
      icon: <Ionicons name="star-outline" size={24} color={Colors.DarkText} />,
      screen: 'SettingsSubscription' as keyof RootStackParamList,
    },
    {
      id: 'support',
      title: 'Support',
      icon: <Ionicons name="help-circle-outline" size={24} color={Colors.DarkText} />,
      screen: 'SettingsSupport' as keyof RootStackParamList,
    },
    {
      id: 'about',
      title: 'About/Legal',
      icon: <Ionicons name="information-circle-outline" size={24} color={Colors.DarkText} />,
      screen: 'SettingsAbout' as keyof RootStackParamList,
    },
  ];

  return (
    <Screen style={styles.container}>
      <View style={styles.header}>
        <Typography variant="heading2">Settings</Typography>
        <TouchableOpacity onPress={handleClose} style={styles.closeButton}>
          <Ionicons name="close" size={24} color={Colors.DarkText} />
        </TouchableOpacity>
      </View>

      <ScrollView style={styles.content}>
        {/* Optional "Finish Setup" card - only shown when phone is not linked */}
        {!isPhoneLinked && (
          <View style={styles.setupCardContainer}>
            <View style={styles.setupCard}>
              <View style={styles.setupCardContent}>
                <Ionicons name="phone-portrait-outline" size={24} color={Colors.DarkText} style={styles.setupCardIcon} />
                <View style={styles.setupCardTextContainer}>
                  <Typography variant="bodyText" style={styles.setupCardTitle}>
                    Finish Setup
                  </Typography>
                  <Typography variant="description" style={styles.setupCardDescription}>
                    Link your phone number for account recovery
                  </Typography>
                </View>
              </View>
              <TouchableOpacity onPress={() => navigateTo('SettingsAccount')}>
                <Ionicons name="chevron-forward" size={20} color={Colors.LightText} />
              </TouchableOpacity>
            </View>
          </View>
        )}

        {/* Settings options */}
        <View style={styles.optionsContainer}>
          {settingsOptions.map((option) => (
            <ListRow
              key={option.id}
              title={option.title}
              leftIcon={option.icon}
              rightIcon={<Ionicons name="chevron-forward" size={20} color={Colors.LightText} />}
              onPress={() => navigateTo(option.screen)}
            />
          ))}
        </View>

        {/* Version number */}
        <View style={styles.versionContainer}>
          <Typography variant="caption" style={styles.versionText}>
            Version 1.0.0
          </Typography>
        </View>
      </ScrollView>
    </Screen>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.BackgroundPrimary,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: Spacing.Large,
    paddingTop: Spacing.Large,
    paddingBottom: Spacing.Medium,
  },
  closeButton: {
    padding: Spacing.Small,
  },
  content: {
    flex: 1,
  },
  setupCardContainer: {
    paddingHorizontal: Spacing.Large,
    paddingVertical: Spacing.Medium,
  },
  setupCard: {
    backgroundColor: Colors.SurfaceSecondary,
    borderRadius: 12,
    padding: Spacing.Medium,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  setupCardContent: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  setupCardIcon: {
    marginRight: Spacing.Medium,
  },
  setupCardTextContainer: {
    flex: 1,
  },
  setupCardTitle: {
    fontWeight: '600',
    marginBottom: 2,
  },
  setupCardDescription: {
    fontSize: 12,
  },
  optionsContainer: {
    marginTop: Spacing.Small,
  },
  versionContainer: {
    alignItems: 'center',
    paddingVertical: Spacing.Large,
    marginTop: Spacing.Large,
  },
  versionText: {
    color: Colors.LightText,
  },
});

export default SettingsScreen;
