import React, { useEffect } from 'react';
import { View, StyleSheet, BackHandler } from 'react-native';
import { useNavigation, useRoute, RouteProp, useFocusEffect } from '@react-navigation/native';
import { NativeStackNavigationProp } from '@react-navigation/native-stack';
import { Colors, Spacing } from '../theme';
import Screen from '../Components/Screen';
import Typography from '../Components/Typography';
import ProgressIndicator from '../Components/ProgressIndicator';
import MinimalistLoadingIndicator from '../Components/MinimalistLoadingIndicator';

type RootStackParamList = {
  CreatingAccount: {
    returnToScan?: boolean;
    scanData?: any;
  };
  AccountCreated: {
    returnToScan?: boolean;
    scanData?: any;
  };
};

type CreatingAccountScreenNavigationProp = NativeStackNavigationProp<
  RootStackParamList,
  'CreatingAccount'
>;

const CreatingAccountScreen: React.FC = () => {
  const navigation = useNavigation<CreatingAccountScreenNavigationProp>();
  const route = useRoute<RouteProp<RootStackParamList, 'CreatingAccount'>>();

  // Prevent hardware back button on Android
  useFocusEffect(
    React.useCallback(() => {
      const onBackPress = () => {
        // Return true to prevent default behavior (going back)
        return true;
      };

      // Add event listener
      const subscription = BackHandler.addEventListener('hardwareBackPress', onBackPress);

      // Clean up event listener on unmount
      return () => subscription.remove();
    }, [])
  );

  useEffect(() => {
    // Get parameters from route
    const { returnToScan, scanData } = route.params || {};

    // Log for debugging
    console.log('CreatingAccount params:', { returnToScan, scanData });

    // Simulate account creation time
    const timer = setTimeout(() => {
      navigation.navigate('AccountCreated', {
        returnToScan,
        scanData
      });
    }, 3000);

    return () => clearTimeout(timer);
  }, [navigation, route.params]);

  return (
    <Screen style={styles.container}>
      <View style={styles.content}>
        <View style={styles.loaderContainer}>
          <MinimalistLoadingIndicator size={120} strokeWidth={4} />
        </View>
        <Typography variant="heading1" style={styles.title}>
          Creating Account
        </Typography>
        <Typography variant="description" style={styles.description}>
          This may take a moment
        </Typography>
      </View>
    </Screen>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.BackgroundPrimary,
  },
  content: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    paddingHorizontal: Spacing.Large,
  },
  loaderContainer: {
    marginBottom: Spacing.XXLarge,
  },
  title: {
    marginBottom: Spacing.Medium,
    textAlign: 'center',
    fontSize: 28,
    fontWeight: 'bold',
  },
  description: {
    textAlign: 'center',
    color: Colors.LightText,
    fontSize: 16,
  },
});

export default CreatingAccountScreen;
