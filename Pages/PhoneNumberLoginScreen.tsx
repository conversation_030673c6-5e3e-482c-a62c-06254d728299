import React, { useState, useEffect } from 'react';
import {
  View,
  StyleSheet,
  TextInput,
  TouchableOpacity,
  KeyboardAvoidingView,
  Platform
} from 'react-native';
import { useNavigation, useRoute, RouteProp } from '@react-navigation/native';
import { NativeStackNavigationProp } from '@react-navigation/native-stack';
import { Colors, Spacing, Shadow } from '../theme';
import Screen from '../Components/Screen';
import Typography from '../Components/Typography';
import CustomButton from '../Components/CustomButton';
import BackButton from '../Components/BackButton';
import { Ionicons } from '@expo/vector-icons';
import { useAuth } from '../context';

type RootStackParamList = {
  PhoneNumberLogin: {
    imageUri?: string;
    returnToScan?: boolean;
    scanData?: {
      imageUri: string;
      apiResponse: any;
    }
  };
  OTPVerification: {
    phoneNumber: string;
    imageUri?: string;
    returnToScan?: boolean;
    scanData?: any;
  };
  CreatingAccount: {
    returnToScan?: boolean;
    scanData?: any;
  };
  ScanResults: {
    imageUri: string;
    isAnonymous: boolean;
    apiResponse: any;
  };
};

type PhoneNumberLoginScreenNavigationProp = NativeStackNavigationProp<
  RootStackParamList,
  'PhoneNumberLogin'
>;

type PhoneNumberLoginScreenRouteProp = RouteProp<RootStackParamList, 'PhoneNumberLogin'>;

const PhoneNumberLoginScreen: React.FC = () => {
  const navigation = useNavigation<PhoneNumberLoginScreenNavigationProp>();
  const route = useRoute<PhoneNumberLoginScreenRouteProp>();
  const { login } = useAuth();
  const [phoneNumber, setPhoneNumber] = useState('');
  const [countryCode, setCountryCode] = useState('+91');
  const [isValid, setIsValid] = useState(false);

  // Validate phone number on change
  useEffect(() => {
    // Simple validation - exactly 10 digits for Indian numbers
    setIsValid(phoneNumber.replace(/\D/g, '').length === 10);
  }, [phoneNumber]);

  const handleBack = () => {
    navigation.goBack();
  };

  const handleContinue = async () => {
    // Call login function from AuthContext
    const fullPhoneNumber = `${countryCode} ${phoneNumber}`;
    await login(fullPhoneNumber);

    // Get parameters from route
    const { returnToScan, scanData, imageUri } = route.params || {};

    // Log for debugging
    console.log('PhoneNumberLogin params:', { returnToScan, scanData, imageUri });

    // Navigate to OTP verification screen with all necessary parameters
    navigation.navigate('OTPVerification', {
      phoneNumber: fullPhoneNumber,
      imageUri,
      returnToScan,
      scanData
    });
  };

  // Format phone number as user types (Indian format)
  const formatPhoneNumber = (text: string) => {
    // Remove all non-digits
    const cleaned = text.replace(/\D/g, '');

    // Format as XXXXX XXXXX (Indian format)
    let formatted = cleaned;
    if (cleaned.length > 0) {
      if (cleaned.length <= 5) {
        formatted = cleaned;
      } else {
        formatted = `${cleaned.slice(0, 5)} ${cleaned.slice(5, 10)}`;
      }
    }

    return formatted;
  };

  const handlePhoneNumberChange = (text: string) => {
    const formatted = formatPhoneNumber(text);
    setPhoneNumber(formatted);
  };

  return (
    <Screen style={styles.container}>
      <KeyboardAvoidingView
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
        style={styles.keyboardAvoidingView}
      >
        <View style={styles.header}>
          <BackButton onPress={handleBack} />
        </View>

        <View style={styles.content}>
          <Typography variant="heading1" style={styles.title}>
            Let's connect your phone number
          </Typography>

          <Typography variant="bodyText" style={styles.description}>
            We'll send you a verification code to confirm your number
          </Typography>

          <View style={styles.inputContainer}>
            <View style={styles.countryCodeContainer}>
              <Typography variant="bodyText" style={styles.countryCode}>
                {countryCode}
              </Typography>
              <Ionicons name="chevron-down" size={16} color={Colors.DarkText} style={styles.codeIcon} />
            </View>

            <TextInput
              style={styles.phoneInput}
              value={phoneNumber}
              onChangeText={handlePhoneNumberChange}
              placeholder="98765 43210"
              placeholderTextColor={Colors.LightText}
              keyboardType="phone-pad"
              maxLength={11} // XXXXX XXXXX
            />
          </View>
        </View>

        <View style={styles.footer}>
          <CustomButton
            title="Continue"
            onPress={handleContinue}
            variant="primary"
            style={[styles.button, Shadow.Medium]}
            disabled={!isValid}
          />
        </View>
      </KeyboardAvoidingView>
    </Screen>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.BackgroundPrimary,
  },
  keyboardAvoidingView: {
    flex: 1,
  },
  header: {
    paddingHorizontal: Spacing.Large,
    paddingTop: Spacing.Large,
  },
  content: {
    flex: 1,
    paddingHorizontal: Spacing.Large,
    paddingTop: Spacing.XXLarge,
  },
  title: {
    marginBottom: Spacing.Medium,
    textAlign: 'center',
  },
  description: {
    textAlign: 'center',
    marginBottom: Spacing.XXLarge,
    color: Colors.LightText,
  },
  inputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: Spacing.Large,
    borderBottomWidth: 1,
    borderBottomColor: Colors.LightText,
    paddingBottom: Spacing.Small,
  },
  countryCodeContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingRight: Spacing.Medium,
    borderRightWidth: 1,
    borderRightColor: Colors.SurfaceSecondary,
  },
  countryCode: {
    fontWeight: '500',
  },
  codeIcon: {
    marginLeft: Spacing.ExtraSmall,
  },
  phoneInput: {
    flex: 1,
    paddingHorizontal: Spacing.Medium,
    paddingVertical: Spacing.Small,
    fontFamily: 'Poppins',
    fontSize: 16,
    color: Colors.DarkText,
  },
  footer: {
    paddingHorizontal: Spacing.Large,
    paddingBottom: Platform.OS === 'ios' ? Spacing.XXLarge : Spacing.Large,
    paddingTop: Spacing.Medium,
  },
  button: {
    width: '100%',
    height: 56,
    borderRadius: 28,
  },
});

export default PhoneNumberLoginScreen;
