import React, { useState, useEffect, useRef } from 'react';
import {
  View,
  StyleSheet,
  TextInput,
  TouchableOpacity,
  KeyboardAvoidingView,
  Platform,
  Keyboard,
} from 'react-native';
import { useNavigation, useRoute, RouteProp } from '@react-navigation/native';
import { NativeStackNavigationProp } from '@react-navigation/native-stack';
import { Colors, Spacing } from '../theme';
import Screen from '../Components/Screen';
import Typography from '../Components/Typography';
import GlowingButton from '../Components/GlowingButton';
import BackButton from '../Components/BackButton';
import { useAuth } from '../context';

type RootStackParamList = {
  OTPVerification: {
    phoneNumber: string;
    imageUri?: string;
    returnToScan?: boolean;
    scanData?: any;
  };
  PhoneNumberLogin: {
    imageUri?: string;
    returnToScan?: boolean;
    scanData?: any;
  };
  CreatingAccount: {
    returnToScan?: boolean;
    scanData?: any;
  };
};

type OTPVerificationScreenNavigationProp = NativeStackNavigationProp<
  RootStackParamList,
  'OTPVerification'
>;

type OTPVerificationScreenRouteProp = RouteProp<RootStackParamList, 'OTPVerification'>;

const OTPVerificationScreen: React.FC = () => {
  const navigation = useNavigation<OTPVerificationScreenNavigationProp>();
  const route = useRoute<OTPVerificationScreenRouteProp>();
  const { verifyOTP } = useAuth();

  // Extract phone number from route params
  const phoneNumber = route.params?.phoneNumber || '(*************';

  // State for OTP digits
  const [otp, setOtp] = useState<string[]>(Array(6).fill(''));
  const [isOtpValid, setIsOtpValid] = useState(false);
  const [timer, setTimer] = useState(30);
  const [isResendActive, setIsResendActive] = useState(false);

  // Refs for TextInput elements
  const inputRefs = useRef<Array<TextInput | null>>([null, null, null, null, null, null]);

  // Timer effect
  useEffect(() => {
    let interval: NodeJS.Timeout;

    if (timer > 0) {
      interval = setInterval(() => {
        setTimer((prevTimer) => {
          if (prevTimer <= 1) {
            setIsResendActive(true);
            clearInterval(interval);
            return 0;
          }
          return prevTimer - 1;
        });
      }, 1000);
    }

    return () => {
      if (interval) clearInterval(interval);
    };
  }, [timer]);

  // Validate OTP
  useEffect(() => {
    const isValid = otp.every((digit) => digit !== '');
    setIsOtpValid(isValid);
  }, [otp]);

  // Handle OTP input change
  const handleOtpChange = (text: string, index: number) => {
    // Allow only digits
    const digit = text.replace(/[^0-9]/g, '');

    // Update OTP state
    const newOtp = [...otp];
    newOtp[index] = digit;
    setOtp(newOtp);

    // If digit is entered and not the last input, focus next input
    if (digit && index < 5) {
      inputRefs.current[index + 1]?.focus();
    }

    // If last digit is entered, dismiss keyboard
    if (digit && index === 5) {
      Keyboard.dismiss();
    }
  };

  // Handle key press (for backspace)
  const handleKeyPress = (e: any, index: number) => {
    if (e.nativeEvent.key === 'Backspace' && !otp[index] && index > 0) {
      // If current input is empty and backspace is pressed, focus previous input
      inputRefs.current[index - 1]?.focus();
    }
  };

  // Handle resend OTP
  const handleResend = () => {
    if (isResendActive) {
      // Reset timer
      setTimer(30);
      setIsResendActive(false);

      // Reset OTP
      setOtp(Array(6).fill(''));
      inputRefs.current[0]?.focus();

      // Simulate OTP resend (would be an API call in a real app)
      console.log('Resending OTP to', phoneNumber);
    }
  };

  // Handle back button press
  const handleBack = () => {
    navigation.goBack();
  };

  // Handle verify button press
  const handleVerify = async () => {
    console.log('handleVerify called, isOtpValid:', isOtpValid);

    // For testing, allow verification even if OTP is not complete
    const otpString = otp.join('');
    console.log('OTP string:', otpString);

    try {
      // Verify OTP using AuthContext
      console.log('Calling verifyOTP with:', phoneNumber, otpString);
      const success = await verifyOTP(phoneNumber, otpString);
      console.log('verifyOTP result:', success);

      if (success) {
        // Get parameters from route
        const { returnToScan, scanData } = route.params || {};

        // Log for debugging
        console.log('OTPVerification params:', { returnToScan, scanData });

        console.log('Navigation to CreatingAccount');
        navigation.navigate('CreatingAccount', {
          returnToScan,
          scanData
        });
      } else {
        // Handle OTP verification failure
        console.log('OTP verification failed');
      }
    } catch (error) {
      console.error('Error in handleVerify:', error);
    }
  };

  // Format phone number for display
  const formatPhoneForDisplay = (phone: string) => {
    return phone;
  };

  return (
    <Screen style={styles.container}>
      <KeyboardAvoidingView
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
        style={styles.keyboardAvoidingView}
      >
        <View style={styles.header}>
          <BackButton onPress={handleBack} />
        </View>

        <View style={styles.content}>
          <Typography variant="heading1" style={styles.title}>
            Enter verification code
          </Typography>

          <Typography variant="bodyText" style={styles.description}>
            We've sent a 6-digit code to {formatPhoneForDisplay(phoneNumber)}
          </Typography>

          <View style={styles.otpContainer}>
            {Array(6).fill(null).map((_, index) => (
              <TextInput
                key={`otp-${index}`}
                ref={(ref) => {
                  if (ref) {
                    inputRefs.current[index] = ref;
                  }
                }}
                style={styles.otpInput}
                value={otp[index]}
                onChangeText={(text) => handleOtpChange(text, index)}
                onKeyPress={(e) => handleKeyPress(e, index)}
                keyboardType="number-pad"
                maxLength={1}
                selectTextOnFocus
                autoFocus={index === 0}
              />
            ))}
          </View>

          <TouchableOpacity
            onPress={handleResend}
            disabled={!isResendActive}
            style={styles.resendContainer}
          >
            <Typography
              variant="description"
              style={isResendActive ? styles.resendActiveText : styles.resendText}
            >
              {isResendActive
                ? 'Resend code'
                : `Didn't get the code? Please wait ${timer}s`}
            </Typography>
          </TouchableOpacity>
        </View>

        <View style={styles.footer}>
          <GlowingButton
            title="Verify"
            onPress={handleVerify}
            variant="primary"
            style={styles.button}
            disabled={!isOtpValid} // Disable button until all 6 digits are entered
            glowIntensity={0.8}
            alwaysGlow={isOtpValid} // Only glow when valid
            glowColor={Colors.AccentBlue}
            // Use faster pulse for more prominence when active
            glowOnPress={false}
          />


        </View>
      </KeyboardAvoidingView>
    </Screen>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.BackgroundPrimary,
  },
  keyboardAvoidingView: {
    flex: 1,
  },
  header: {
    paddingHorizontal: Spacing.Large,
    paddingTop: Spacing.Large,
  },
  content: {
    flex: 1,
    paddingHorizontal: Spacing.Large,
    paddingTop: Spacing.XXLarge,
  },
  title: {
    marginBottom: Spacing.Medium,
    textAlign: 'center',
  },
  description: {
    textAlign: 'center',
    marginBottom: Spacing.XXLarge,
    color: Colors.LightText,
  },
  otpContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: Spacing.Large,
  },
  otpInput: {
    width: 45,
    height: 55,
    borderWidth: 1,
    borderColor: Colors.SurfaceSecondary,
    borderRadius: 8, // Small border radius
    textAlign: 'center',
    fontSize: 24,
    fontWeight: 'bold',
    color: Colors.DarkText,
    backgroundColor: Colors.SurfaceSecondary,
  },
  resendContainer: {
    alignItems: 'center',
    marginTop: Spacing.Medium,
  },
  resendText: {
    color: Colors.LightText,
  },
  resendActiveText: {
    color: Colors.AccentBlue,
    fontWeight: '500',
  },
  footer: {
    paddingHorizontal: Spacing.Large,
    paddingBottom: Platform.OS === 'ios' ? Spacing.XXLarge : Spacing.Large,
    paddingTop: Spacing.Medium,
  },
  button: {
    width: '100%',
    height: 56,
    borderRadius: 28,
    marginBottom: Spacing.Medium,
  },

});

export default OTPVerificationScreen;
