import React from 'react';
import { View, StyleSheet, ScrollView, Alert } from 'react-native';
import { useNavigation } from '@react-navigation/native';
import { NativeStackNavigationProp } from '@react-navigation/native-stack';
import { Colors, Spacing } from '../theme';
import Screen from '../Components/Screen';
import Typography from '../Components/Typography';
import BackButton from '../Components/BackButton';
import ListRow from '../Components/ListRow';
import { Ionicons } from '@expo/vector-icons';

type RootStackParamList = {
  Settings: undefined;
  SettingsSupport: undefined;
};

type SettingsSupportScreenNavigationProp = NativeStackNavigationProp<
  RootStackParamList,
  'SettingsSupport'
>;

// Support options
const supportOptions = [
  {
    id: 'faq',
    title: 'Frequently Asked Questions',
    icon: <Ionicons name="help-circle-outline" size={24} color={Colors.DarkText} />,
    action: () => console.log('Navigate to FAQ'),
  },
  {
    id: 'contact',
    title: 'Contact Us',
    icon: <Ionicons name="mail-outline" size={24} color={Colors.DarkText} />,
    action: () => console.log('Navigate to Contact Us'),
  },
  {
    id: 'report',
    title: 'Report a Bug',
    icon: <Ionicons name="bug-outline" size={24} color={Colors.DarkText} />,
    action: () => console.log('Navigate to Report a Bug'),
  },
  {
    id: 'privacy',
    title: 'Privacy Policy',
    icon: <Ionicons name="shield-outline" size={24} color={Colors.DarkText} />,
    action: () => console.log('Open Privacy Policy'),
  },
  {
    id: 'terms',
    title: 'Terms of Service',
    icon: <Ionicons name="document-text-outline" size={24} color={Colors.DarkText} />,
    action: () => console.log('Open Terms of Service'),
  },
];

const SettingsSupportScreen: React.FC = () => {
  const navigation = useNavigation<SettingsSupportScreenNavigationProp>();

  const handleBack = () => {
    navigation.navigate('Settings');
  };

  const handleOptionPress = (option: typeof supportOptions[0]) => {
    // For this mock implementation, just show an alert
    Alert.alert(
      option.title,
      `You selected ${option.title}. This would navigate to the ${option.title} screen or open an external link in a real app.`,
      [{ text: 'OK', onPress: option.action }]
    );
  };

  return (
    <Screen style={styles.container}>
      <View style={styles.header}>
        <BackButton onPress={handleBack} />
        <Typography variant="heading1" style={styles.title}>
          Support
        </Typography>
      </View>

      <ScrollView style={styles.content}>
        <View style={styles.optionsContainer}>
          {supportOptions.map((option) => (
            <ListRow
              key={option.id}
              title={option.title}
              leftIcon={option.icon}
              rightIcon={<Ionicons name="chevron-forward" size={20} color={Colors.LightText} />}
              onPress={() => handleOptionPress(option)}
            />
          ))}
        </View>

        <View style={styles.versionContainer}>
          <Typography variant="caption" style={styles.versionText}>
            App Version: 1.0.0
          </Typography>
          <Typography variant="caption" style={styles.versionText}>
            © 2023 JunkChk. All rights reserved.
          </Typography>
        </View>
      </ScrollView>
    </Screen>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.BackgroundPrimary,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: Spacing.Large,
    paddingTop: Spacing.Large,
    paddingBottom: Spacing.Medium,
  },
  title: {
    marginLeft: Spacing.Medium,
  },
  content: {
    flex: 1,
  },
  optionsContainer: {
    marginTop: Spacing.Small,
  },
  versionContainer: {
    alignItems: 'center',
    paddingVertical: Spacing.Large,
    marginTop: Spacing.XXLarge,
  },
  versionText: {
    color: Colors.LightText,
    marginBottom: Spacing.Small,
  },
});

export default SettingsSupportScreen;
