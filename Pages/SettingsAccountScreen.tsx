import React, { useState } from 'react';
import { View, StyleSheet, ScrollView, TouchableOpacity } from 'react-native';
import { useNavigation } from '@react-navigation/native';
import { NativeStackNavigationProp } from '@react-navigation/native-stack';
import { Colors, Spacing } from '../theme';
import Screen from '../Components/Screen';
import Typography from '../Components/Typography';
import Card from '../Components/Card';
import BackButton from '../Components/BackButton';
import ListRow from '../Components/ListRow';
import ConfirmDeletionModal from '../Components/ConfirmDeletionModal';
import { Ionicons } from '@expo/vector-icons';
import { useAuth } from '../context';

type RootStackParamList = {
  Settings: undefined;
  SettingsAccount: undefined;
  PhoneNumberLogin: { imageUri?: string };
  ConfirmDeletion: undefined;
  Welcome: undefined;
};

type SettingsAccountScreenNavigationProp = NativeStackNavigationProp<
  RootStackParamList,
  'SettingsAccount'
>;

const SettingsAccountScreen: React.FC = () => {
  const navigation = useNavigation<SettingsAccountScreenNavigationProp>();
  const { authState, logout, clearUserData } = useAuth();
  const [isDeleteModalVisible, setIsDeleteModalVisible] = useState(false);

  // Check if phone is linked based on authentication state
  const isPhoneLinked = authState.isAuthenticated && authState.user?.phoneNumber;

  const handleBack = () => {
    navigation.navigate('Settings');
  };

  const handleLinkPhone = () => {
    // Navigate to phone number login screen
    navigation.navigate('PhoneNumberLogin', {});
  };

  const handleDeleteAccount = () => {
    // Show the confirm deletion modal
    setIsDeleteModalVisible(true);
  };

  const handleCloseDeleteModal = () => {
    setIsDeleteModalVisible(false);
  };

  const handleConfirmDelete = async () => {
    // In a real app, this would call an API to delete the account
    console.log('Account data deleted');
    setIsDeleteModalVisible(false);

    // Clear all user data including history
    await clearUserData();

    // Navigate back to the Welcome screen after deletion
    // This would typically happen after the API call is successful
    setTimeout(() => {
      navigation.navigate('Welcome');
    }, 500);
  };

  return (
    <Screen style={styles.container}>
      <View style={styles.header}>
        <BackButton onPress={handleBack} />
        <Typography variant="heading1" style={styles.title}>
          Account
        </Typography>
      </View>

      {/* Confirm Deletion Modal */}
      <ConfirmDeletionModal
        visible={isDeleteModalVisible}
        onClose={handleCloseDeleteModal}
        onDelete={handleConfirmDelete}
      />

      <ScrollView style={styles.content}>
        {/* Account Info Section */}
        <View style={styles.section}>
          <Typography variant="heading2" style={styles.sectionTitle}>
            Account Information
          </Typography>
          <Card style={styles.card}>
            <View style={styles.accountInfoRow}>
              <Typography variant="bodyText" style={styles.accountInfoLabel}>
                User ID
              </Typography>
              <Typography variant="description" style={styles.accountInfoValue}>
                user_123456789
              </Typography>
            </View>
            <View style={styles.accountInfoRow}>
              <Typography variant="bodyText" style={styles.accountInfoLabel}>
                Account Type
              </Typography>
              <Typography variant="description" style={styles.accountInfoValue}>
                Free
              </Typography>
            </View>
          </Card>
        </View>

        {/* Account Recovery Section */}
        <View style={styles.section}>
          <Typography variant="heading2" style={styles.sectionTitle}>
            Account Recovery
          </Typography>
          <Card style={styles.card}>
            <Typography variant="bodyText" style={styles.sectionDescription}>
              Link your phone number to recover your account if you lose access.
            </Typography>
            <ListRow
              title={isPhoneLinked ? "Phone Number Linked" : "Link Phone Number"}
              leftIcon={<Ionicons name="phone-portrait-outline" size={24} color={Colors.DarkText} />}
              rightIcon={
                isPhoneLinked ? (
                  <Ionicons name="checkmark-circle" size={24} color={Colors.Success} />
                ) : (
                  <Ionicons name="chevron-forward" size={20} color={Colors.LightText} />
                )
              }
              onPress={handleLinkPhone}
              showSeparator={false}
            />
          </Card>
        </View>

        {/* Data Deletion Section */}
        <View style={styles.section}>
          <Typography variant="heading2" style={styles.sectionTitle}>
            Data Deletion
          </Typography>
          <Card style={styles.card}>
            <Typography variant="bodyText" style={styles.sectionDescription}>
              Permanently delete all your account data, including scan history and preferences.
            </Typography>
            <TouchableOpacity
              style={styles.deleteButton}
              onPress={handleDeleteAccount}
            >
              <Ionicons name="trash-outline" size={20} color={Colors.Error} style={styles.deleteButtonIcon} />
              <Typography variant="bodyText" style={styles.deleteButtonText}>
                Delete Account Data
              </Typography>
            </TouchableOpacity>
          </Card>
        </View>
      </ScrollView>
    </Screen>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.BackgroundPrimary,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: Spacing.Large,
    paddingTop: Spacing.Large,
    paddingBottom: Spacing.Medium,
  },
  title: {
    marginLeft: Spacing.Medium,
  },
  content: {
    flex: 1,
  },
  section: {
    marginBottom: Spacing.Large,
    paddingHorizontal: Spacing.Large,
  },
  sectionTitle: {
    marginBottom: Spacing.Medium,
  },
  sectionDescription: {
    marginBottom: Spacing.Medium,
    color: Colors.LightText,
  },
  card: {
    width: '100%',
  },
  accountInfoRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: Spacing.Small,
  },
  accountInfoLabel: {
    fontWeight: '600',
  },
  accountInfoValue: {
    color: Colors.LightText,
  },
  deleteButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: Spacing.Medium,
  },
  deleteButtonIcon: {
    marginRight: Spacing.Small,
  },
  deleteButtonText: {
    color: Colors.Error,
    fontWeight: '600',
  },
});

export default SettingsAccountScreen;
