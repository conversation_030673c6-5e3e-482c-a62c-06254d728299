import React, { useEffect } from 'react';
import { View, StyleSheet } from 'react-native';
import { useNavigation } from '@react-navigation/native';
import { NativeStackNavigationProp } from '@react-navigation/native-stack';
import { Colors, Spacing } from '../theme';
import Screen from '../Components/Screen';
import Animated, {
  useSharedValue,
  useAnimatedStyle,
  withTiming,
  withDelay,
  withSequence,
  Easing,
} from 'react-native-reanimated';
import { AnimatedText, AnimatedView, AnimatedButton } from '../Components/AnimatedComponents';
import { AnimationDuration, AnimationEasing } from '../Components/animations';

type RootStackParamList = {
  StartPersonalization: undefined;
  YourGoals: undefined;
  HowJunkChkWorks: undefined;
};

type StartPersonalizationScreenNavigationProp = NativeStackNavigationProp<
  RootStackParamList,
  'StartPersonalization'
>;

const StartPersonalizationScreen: React.FC = () => {
  const navigation = useNavigation<StartPersonalizationScreenNavigationProp>();

  // Background animation values
  const backgroundAnim1 = useSharedValue(0);
  const backgroundAnim2 = useSharedValue(0);

  // Start background animations on mount
  useEffect(() => {
    // First background element animation
    backgroundAnim1.value = withSequence(
      withTiming(1, {
        duration: 15000,
        easing: Easing.inOut(Easing.ease)
      }),
      withTiming(0, {
        duration: 15000,
        easing: Easing.inOut(Easing.ease)
      })
    );

    // Second background element animation with delay
    backgroundAnim2.value = withDelay(
      2000,
      withSequence(
        withTiming(1, {
          duration: 18000,
          easing: Easing.inOut(Easing.ease)
        }),
        withTiming(0, {
          duration: 18000,
          easing: Easing.inOut(Easing.ease)
        })
      )
    );

    return () => {
      // Cleanup
      backgroundAnim1.value = 0;
      backgroundAnim2.value = 0;
    };
  }, []);

  // Animated background styles
  const animatedBackgroundStyle1 = useAnimatedStyle(() => {
    return {
      opacity: 0.05,
      transform: [
        { scale: 1 + (backgroundAnim1.value * 0.1) },
        { rotate: `${backgroundAnim1.value * 10}deg` },
      ],
    };
  });

  const animatedBackgroundStyle2 = useAnimatedStyle(() => {
    return {
      opacity: 0.03,
      transform: [
        { scale: 1 + (backgroundAnim2.value * 0.15) },
        { rotate: `${-backgroundAnim2.value * 15}deg` },
      ],
    };
  });

  const handleContinue = () => {
    navigation.navigate('YourGoals');
  };

  const handleSkip = () => {
    navigation.navigate('HowJunkChkWorks');
  };

  return (
    <Screen style={styles.container}>
      {/* Animated background elements */}
      <Animated.View style={[styles.backgroundElement1, animatedBackgroundStyle1]} />
      <Animated.View style={[styles.backgroundElement2, animatedBackgroundStyle2]} />

      <AnimatedView
        style={styles.content}
        delayMs={300}
        duration={800}
        direction="up"
      >
        <AnimatedText
          variant="heading1"
          style={styles.title}
          delayMs={300}
          duration={800}
          direction="up"
        >
          Let's Personalize Your JunkChk
        </AnimatedText>
        <AnimatedText
          variant="bodyText"
          style={styles.description}
          delayMs={500}
          duration={800}
          direction="up"
        >
          A few quick questions will help us tailor your experience and highlight what matters most to you.
        </AnimatedText>
      </AnimatedView>

      <AnimatedView
        style={styles.footer}
        delayMs={700}
        duration={800}
        direction="up"
      >
        <AnimatedButton
          title="Continue"
          onPress={handleContinue}
          variant="primary"
          style={styles.primaryButton}
          glowOnPress={true}
          glowIntensity={0.4}
          scaleOnPress={true}
        />
        <AnimatedButton
          title="Skip Personalization"
          onPress={handleSkip}
          variant="tertiary"
          style={styles.secondaryButton}
          delayMs={200}
          scaleOnPress={true}
          scaleFactor={0.97}
        />
      </AnimatedView>
    </Screen>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.BackgroundPrimary,
    overflow: 'hidden',
  },
  backgroundElement1: {
    position: 'absolute',
    top: -150,
    left: -100,
    width: 400,
    height: 400,
    borderRadius: 200,
    backgroundColor: Colors.DarkText,
    opacity: 0.05,
  },
  backgroundElement2: {
    position: 'absolute',
    bottom: -200,
    right: -150,
    width: 500,
    height: 500,
    borderRadius: 250,
    backgroundColor: Colors.DarkText,
    opacity: 0.03,
  },
  content: {
    flex: 1,
    justifyContent: 'center',
    paddingHorizontal: Spacing.Large,
  },
  title: {
    marginBottom: Spacing.Large,
  },
  description: {
    marginBottom: Spacing.ExtraLarge,
    color: Colors.LightText,
  },
  footer: {
    paddingHorizontal: Spacing.Large,
    paddingBottom: Spacing.ExtraLarge,
  },
  primaryButton: {
    width: '100%',
    marginBottom: Spacing.Medium,
  },
  secondaryButton: {
    width: '100%',
  },
});

export default StartPersonalizationScreen;
