import { Platform, Dimensions } from 'react-native';

/**
 * High refresh rate optimization utilities
 * Enables maximum refresh rate support for both Android and iOS devices
 */

// Get optimal frame timing for animations
export const getOptimalFrameTiming = (): number => {
  // For high refresh rate displays (120Hz, 90Hz), use faster timing
  // For standard displays (60Hz), use standard timing
  
  if (Platform.OS === 'ios') {
    // iOS ProMotion displays support up to 120Hz
    return 8; // ~120fps timing
  } else if (Platform.OS === 'android') {
    // Android high refresh rate displays (90Hz, 120Hz, 144Hz)
    return 8; // ~120fps timing
  }
  
  return 16; // Fallback to 60fps
};

// Get optimal animation increment for smooth progress
export const getOptimalAnimationIncrement = (): number => {
  // Smaller increments for higher refresh rates
  if (Platform.OS === 'ios' || Platform.OS === 'android') {
    return 0.003; // Ultra-smooth increment for high refresh rate
  }
  
  return 0.005; // Standard increment
};

// Animation configuration optimized for high refresh rate
export const getHighRefreshRateAnimationConfig = (duration: number) => ({
  duration,
  useNativeDriver: true,
  isInteraction: false, // Don't delay other interactions
});

// Optimized easing for smooth animations
export const smoothEasing = {
  in: (t: number) => t * t * t,
  out: (t: number) => --t * t * t + 1,
  inOut: (t: number) => t < 0.5 ? 4 * t * t * t : (t - 1) * (2 * t - 2) * (2 * t - 2) + 1,
};

// Enable high refresh rate for the current screen
export const enableHighRefreshRate = () => {
  // This is handled at the app level through app.json configuration
  // Additional native optimizations can be added here if needed
  
  if (Platform.OS === 'android') {
    // Android-specific optimizations
    // These would require native module implementation
    console.log('High refresh rate enabled for Android');
  } else if (Platform.OS === 'ios') {
    // iOS-specific optimizations (ProMotion)
    console.log('ProMotion high refresh rate enabled for iOS');
  }
};

// Check if device supports high refresh rate
export const supportsHighRefreshRate = (): boolean => {
  if (Platform.OS === 'ios') {
    // Most modern iOS devices support ProMotion (120Hz)
    return true;
  } else if (Platform.OS === 'android') {
    // Many modern Android devices support 90Hz, 120Hz, or higher
    return true;
  }
  
  return false;
};

// Get recommended animation settings based on device capabilities
export const getAnimationSettings = () => {
  const frameTiming = getOptimalFrameTiming();
  const increment = getOptimalAnimationIncrement();
  const supportsHRR = supportsHighRefreshRate();
  
  return {
    frameTiming,
    increment,
    supportsHighRefreshRate: supportsHRR,
    animationConfig: getHighRefreshRateAnimationConfig,
    easing: smoothEasing,
  };
};
