/**
 * Network Test Utility
 * Test network connectivity and server availability
 */

import { SERVER_CONFIG } from '../config/serverConfig';

export const testNetworkConnectivity = async () => {
  console.log('🔍 Testing Network Connectivity...');
  
  try {
    // Test 1: Basic fetch to server health endpoint
    console.log('1. Testing server health endpoint...');
    console.log('   Server URL:', SERVER_CONFIG.baseUrl);
    
    const healthUrl = `${SERVER_CONFIG.baseUrl}/health`;
    console.log('   Health URL:', healthUrl);
    
    const response = await fetch(healthUrl, {
      method: 'GET',
      timeout: 10000,
      headers: {
        'Accept': 'application/json',
        'Content-Type': 'application/json',
      }
    });
    
    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`);
    }
    
    const data = await response.json();
    console.log('✅ Server health check successful:', data);
    
    // Test 2: Test API status endpoint
    console.log('2. Testing API status endpoint...');
    const statusUrl = `${SERVER_CONFIG.baseUrl}/api/status`;
    
    const statusResponse = await fetch(statusUrl, {
      method: 'GET',
      timeout: 10000,
      headers: {
        'Accept': 'application/json',
        'Content-Type': 'application/json',
      }
    });
    
    if (!statusResponse.ok) {
      throw new Error(`HTTP ${statusResponse.status}: ${statusResponse.statusText}`);
    }
    
    const statusData = await statusResponse.json();
    console.log('✅ API status check successful:', statusData);
    
    return {
      success: true,
      health: data,
      status: statusData,
      serverUrl: SERVER_CONFIG.baseUrl
    };
    
  } catch (error) {
    console.error('❌ Network connectivity test failed:', error);
    console.error('   Error details:', {
      message: error.message,
      code: error.code,
      name: error.name
    });
    
    return {
      success: false,
      error: error.message,
      serverUrl: SERVER_CONFIG.baseUrl
    };
  }
};

export const diagnoseNetworkIssues = async () => {
  console.log('🔧 Diagnosing Network Issues...');
  
  const result = await testNetworkConnectivity();
  
  if (result.success) {
    console.log('✅ Network connectivity is working properly');
    return result;
  }
  
  console.log('❌ Network connectivity issues detected');
  console.log('🔍 Possible causes:');
  
  if (result.error.includes('Network request failed')) {
    console.log('   - Server is not running');
    console.log('   - Wrong server URL configuration');
    console.log('   - Firewall blocking connection');
  }
  
  if (result.error.includes('timeout')) {
    console.log('   - Server is slow to respond');
    console.log('   - Network latency issues');
    console.log('   - Server overloaded');
  }
  
  if (result.error.includes('ECONNREFUSED')) {
    console.log('   - Server is not running on the specified port');
    console.log('   - Port is blocked');
  }
  
  console.log('💡 Suggested fixes:');
  console.log('   1. Verify server is running: npm run dev');
  console.log('   2. Check server URL in config:', result.serverUrl);
  console.log('   3. For iOS simulator, use localhost:3000');
  console.log('   4. For physical device, use your computer\'s IP address');
  
  return result;
};
