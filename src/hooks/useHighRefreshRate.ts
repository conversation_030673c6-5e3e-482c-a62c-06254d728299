import { useEffect } from 'react';
import { Platform, InteractionManager } from 'react-native';
import { getAnimationSettings, enableHighRefreshRate } from '../utils/refreshRate';

/**
 * Hook to enable high refresh rate optimizations for the current component
 * This should be used in screens that have animations or smooth interactions
 */
export const useHighRefreshRate = () => {
  const animationSettings = getAnimationSettings();

  useEffect(() => {
    // Enable high refresh rate when component mounts
    enableHighRefreshRate();

    // Ensure animations run after interactions complete
    const task = InteractionManager.runAfterInteractions(() => {
      // Additional optimizations can be added here
      if (Platform.OS === 'android') {
        // Android-specific optimizations
        console.log('Android high refresh rate optimizations enabled');
      } else if (Platform.OS === 'ios') {
        // iOS ProMotion optimizations
        console.log('iOS ProMotion optimizations enabled');
      }
    });

    return () => {
      task.cancel();
    };
  }, []);

  return animationSettings;
};

/**
 * Hook for optimized animation timing
 * Returns the best frame timing for the current device
 */
export const useOptimizedTiming = () => {
  const settings = getAnimationSettings();
  
  return {
    frameTiming: settings.frameTiming,
    increment: settings.increment,
    animationConfig: settings.animationConfig,
    easing: settings.easing,
  };
};

/**
 * Hook for creating smooth animated values with high refresh rate support
 */
export const useSmoothAnimation = (initialValue: number = 0) => {
  const settings = useOptimizedTiming();
  
  return {
    settings,
    createAnimationConfig: (duration: number) => ({
      duration,
      useNativeDriver: true,
      isInteraction: false,
      easing: settings.easing.out,
    }),
  };
};
