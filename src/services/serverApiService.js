/**
 * Server API Service
 * Handles all communication with the Gemini Image Analysis Server
 * Optimized for performance, reliability, and crash prevention
 */

import { SERVER_CONFIG } from '../config/serverConfig';

class ServerApiService {
  constructor() {
    this.baseUrl = SERVER_CONFIG.baseUrl;
    this.timeout = SERVER_CONFIG.timeout;
    this.maxRetries = SERVER_CONFIG.retries;
    this.endpoints = SERVER_CONFIG.endpoints;

    // Performance optimizations
    this.requestCache = new Map();
    this.cacheTimeout = 5 * 60 * 1000; // 5 minutes
    this.activeRequests = new Map();

    // Connection pooling simulation
    this.connectionPool = {
      maxConnections: 5,
      activeConnections: 0,
      queue: []
    };
  }

  /**
   * Generate cache key for request
   * @param {string} endpoint - API endpoint
   * @param {Object} options - Request options
   * @returns {string} - Cache key
   */
  generateCacheKey(endpoint, options) {
    const key = `${endpoint}_${JSON.stringify(options.body || {})}`;
    return btoa(key).replace(/[^a-zA-Z0-9]/g, '').substring(0, 50);
  }

  /**
   * Check if request is cached and valid
   * @param {string} cacheKey - Cache key
   * @returns {Object|null} - Cached response or null
   */
  getCachedResponse(cacheKey) {
    const cached = this.requestCache.get(cacheKey);
    if (cached && Date.now() - cached.timestamp < this.cacheTimeout) {
      return cached.data;
    }
    if (cached) {
      this.requestCache.delete(cacheKey);
    }
    return null;
  }

  /**
   * Cache response
   * @param {string} cacheKey - Cache key
   * @param {Object} data - Response data
   */
  cacheResponse(cacheKey, data) {
    this.requestCache.set(cacheKey, {
      data,
      timestamp: Date.now()
    });
  }

  /**
   * Make HTTP request with timeout, retry logic, and caching
   * @param {string} endpoint - API endpoint
   * @param {Object} options - Request options
   * @returns {Promise<Object>} - Response data
   */
  async makeRequest(endpoint, options = {}) {
    const url = `${this.baseUrl}${endpoint}`;
    const cacheKey = this.generateCacheKey(endpoint, options);

    // Check cache for GET requests
    if (options.method === 'GET') {
      const cached = this.getCachedResponse(cacheKey);
      if (cached) {
        console.log('ServerApiService: Returning cached response');
        return cached;
      }
    }

    // Prevent duplicate requests
    if (this.activeRequests.has(cacheKey)) {
      console.log('ServerApiService: Waiting for existing request');
      return this.activeRequests.get(cacheKey);
    }

    const requestOptions = {
      method: options.method || 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
        'Connection': 'keep-alive',
        ...options.headers
      },
      body: options.body ? JSON.stringify(options.body) : undefined
    };

    // Create optimized timeout promise with AbortController
    const controller = new AbortController();
    requestOptions.signal = controller.signal;

    const timeoutPromise = new Promise((_, reject) => {
      const timeoutId = setTimeout(() => {
        controller.abort();
        reject(new Error('Request timed out'));
      }, this.timeout);
      return timeoutId;
    });

    let lastError;

    // Create request promise and track it
    const requestPromise = this.executeRequest(url, requestOptions, timeoutPromise, cacheKey);
    this.activeRequests.set(cacheKey, requestPromise);

    try {
      const result = await requestPromise;

      // Cache successful responses
      if (options.method === 'GET' || options.cacheable) {
        this.cacheResponse(cacheKey, result);
      }

      return result;
    } finally {
      this.activeRequests.delete(cacheKey);
    }
  }

  /**
   * Execute request with retry logic
   * @param {string} url - Request URL
   * @param {Object} requestOptions - Request options
   * @param {Promise} timeoutPromise - Timeout promise
   * @param {string} cacheKey - Cache key for logging
   * @returns {Promise<Object>} - Response data
   */
  async executeRequest(url, requestOptions, timeoutPromise, cacheKey) {
    let lastError;

    // Retry logic with exponential backoff
    for (let attempt = 1; attempt <= this.maxRetries; attempt++) {
      try {
        console.log(`ServerApiService: Making request to ${url} (attempt ${attempt}/${this.maxRetries})`);

        // Performance monitoring
        const startTime = performance.now();

        // Race between fetch and timeout
        const response = await Promise.race([
          fetch(url, requestOptions),
          timeoutPromise
        ]);

        const endTime = performance.now();
        console.log(`ServerApiService: Request completed in ${(endTime - startTime).toFixed(2)}ms`);

        if (!response.ok) {
          const errorData = await response.json().catch(() => ({}));
          throw new Error(errorData.error?.message || `HTTP ${response.status}: ${response.statusText}`);
        }

        const data = await response.json();
        console.log('ServerApiService: Request successful');
        return data;

      } catch (error) {
        console.error(`ServerApiService: Request failed (attempt ${attempt}):`, error);
        lastError = error;

        // Don't retry on certain errors
        if (error.message.includes('400') ||
            error.message.includes('401') ||
            error.message.includes('403') ||
            error.name === 'AbortError') {
          break;
        }

        // Exponential backoff with jitter
        if (attempt < this.maxRetries) {
          const backoffTime = Math.min(1000 * Math.pow(2, attempt - 1), 10000);
          const jitter = Math.random() * 1000;
          await new Promise(resolve => setTimeout(resolve, backoffTime + jitter));
        }
      }
    }

    throw lastError;
  }

  /**
   * Analyze single image
   * @param {string} base64Image - Base64 encoded image
   * @returns {Promise<Object>} - Analysis result
   */
  async analyzeSingleImage(base64Image, allergies = []) {
    try {
      console.log('ServerApiService: Starting single image analysis');
      console.log('ServerApiService: Image data received:', {
        imageType: typeof base64Image,
        imageLength: base64Image ? base64Image.length : 0,
        imagePreview: base64Image ? base64Image.substring(0, 50) + '...' : 'no image',
        hasImage: !!base64Image
      });

      const response = await this.makeRequest(this.endpoints.analyzeSingleImage, {
        body: {
          images: [base64Image], // Server expects array format
          allergies
        }
      });

      if (response.success && response.data) {
        console.log('ServerApiService: Single image analysis successful');
        return response.data;
      } else {
        throw new Error('Invalid response format from server');
      }
    } catch (error) {
      console.error('ServerApiService: Single image analysis failed:', error);
      throw this.handleError(error);
    }
  }

  /**
   * Analyze multiple images
   * @param {Array<string>} base64Images - Array of base64 encoded images
   * @param {Array<string>} allergies - Allergies
   * @returns {Promise<Object>} - Analysis result
   */
  async analyzeMultipleImages(base64Images, allergies = []) {
    try {
      console.log('ServerApiService: Starting multiple image analysis', { imageCount: base64Images.length });

      const response = await this.makeRequest(this.endpoints.analyzeMultipleImages, {
        body: {
          images: base64Images,
          allergies
        }
      });

      if (response.success && response.data) {
        console.log('ServerApiService: Multiple image analysis successful');
        return response.data;
      } else {
        throw new Error('Invalid response format from server');
      }
    } catch (error) {
      console.error('ServerApiService: Multiple image analysis failed:', error);
      throw this.handleError(error);
    }
  }

  /**
   * Check server health
   * @returns {Promise<Object>} - Health status
   */
  async checkHealth() {
    try {
      const response = await this.makeRequest(this.endpoints.health, {
        method: 'GET'
      });
      return response;
    } catch (error) {
      console.error('ServerApiService: Health check failed:', error);
      throw error;
    }
  }

  /**
   * Get server status
   * @returns {Promise<Object>} - Server status
   */
  async getStatus() {
    try {
      const response = await this.makeRequest(this.endpoints.status, {
        method: 'GET'
      });
      return response;
    } catch (error) {
      console.error('ServerApiService: Status check failed:', error);
      throw error;
    }
  }

  /**
   * Handle and format errors
   * @param {Error} error - Original error
   * @returns {Error} - Formatted error
   */
  handleError(error) {
    console.log('ServerApiService: Handling error:', error.message);

    // Network errors (React Native specific)
    if (error.message.includes('Network request failed') ||
        error.message.includes('fetch') ||
        error.message.includes('Connection refused') ||
        error.message.includes('ECONNREFUSED')) {
      const networkError = new Error('Cannot connect to server. Please check your internet connection and ensure the server is running.');
      networkError.code = 'NETWORK_ERROR';
      return networkError;
    }

    // Timeout errors
    if (error.message.includes('timed out') ||
        error.message.includes('timeout') ||
        error.message.includes('Request timed out')) {
      const timeoutError = new Error('Request timed out. Please try again.');
      timeoutError.code = 'TIMEOUT_ERROR';
      return timeoutError;
    }

    // Server errors
    if (error.message.includes('500')) {
      const serverError = new Error('Server error occurred. Please try again later.');
      serverError.code = 'SERVER_ERROR';
      return serverError;
    }

    // Rate limiting
    if (error.message.includes('429')) {
      const rateLimitError = new Error('Too many requests. Please wait and try again.');
      rateLimitError.code = 'RATE_LIMIT_ERROR';
      return rateLimitError;
    }

    // CORS errors
    if (error.message.includes('CORS') || error.message.includes('cors')) {
      const corsError = new Error('Server configuration error. Please contact support.');
      corsError.code = 'CORS_ERROR';
      return corsError;
    }

    // Default error
    const defaultError = new Error(error.message || 'An unexpected error occurred.');
    defaultError.code = 'UNKNOWN_ERROR';
    return defaultError;
  }

  /**
   * Test server connection
   * @returns {Promise<Object>} - Connection test result
   */
  async testConnection() {
    try {
      console.log('ServerApiService: Testing connection to:', this.baseUrl);
      const startTime = Date.now();

      const health = await this.checkHealth();
      const endTime = Date.now();

      return {
        success: true,
        responseTime: endTime - startTime,
        serverStatus: health.status,
        message: 'Connection successful'
      };
    } catch (error) {
      console.error('ServerApiService: Connection test failed:', error);
      return {
        success: false,
        error: error.message,
        message: 'Connection failed'
      };
    }
  }

  /**
   * Update server configuration
   * @param {Object} config - New configuration
   */
  updateConfig(config) {
    if (config.baseUrl) {
      this.baseUrl = config.baseUrl;
      console.log('ServerApiService: Updated base URL to:', this.baseUrl);
    }
    if (config.timeout) {
      this.timeout = config.timeout;
      console.log('ServerApiService: Updated timeout to:', this.timeout);
    }
  }
}

// Export singleton instance
export default new ServerApiService();
